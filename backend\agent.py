from autogen import AssistantAgent, UserProxyAgent, GroupChat, GroupChatManager
import json
import os
from typing import Dict
import datetime
import re
from json_repair import repair_json
import dirtyjson
from llama_index.core.output_parsers.utils import parse_json_markdown
from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import sys
from pydantic import BaseModel
import io
from contextlib import redirect_stdout

# Initialize FastAPI app
app = FastAPI(
    title="AI Job Transformation API",
    description="API for analyzing job positions and their AI transformation potential",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

class JobRequest(BaseModel):
    job_position: str

# API endpoint for job transformation analysis
@app.post("/analyze-job")
async def analyze_job_endpoint(request: JobRequest):
    job_position = request.job_position
    try:
        # Capture all print output
        f = io.StringIO()
        with redirect_stdout(f):
            # Validate job position
            is_valid, error_message = validate_job_position(job_position)
            if not is_valid:
                raise HTTPException(status_code=400, detail=error_message)
            
            # Get transformation results
            transformation_results = analyze_job_for_ai_transformation(job_position)
            
            if transformation_results.get("status") == "error":
                raise HTTPException(
                    status_code=500,
                    detail=f"Analysis failed: {transformation_results.get('message')}"
                )
        backend_logs = f.getvalue()
        # Add backend_logs to the response
        transformation_results["backend_logs"] = backend_logs
        return transformation_results
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Configuration
config_list_bedrock = [
    {
        "api_type": "bedrock",
        "model": "anthropic.claude-3-haiku-20240307-v1:0",
        "aws_region": "us-east-1",
        "aws_access_key": "********************",
        "aws_secret_key": "ghYKGewFG7lnQ+uhBxSGB48r+x2CCxwnpHLrpDbN",
        "temperature": 0.5, # Adjusted for balanced creativity and accuracy
    }
]
llm_config = {
    "config_list": config_list_bedrock,
}

# Agents
agent_job_describer = AssistantAgent(
    name="Job_Describer",
    system_message="""
    You are an expert in job analysis and description.
    Your role is to:
    1. Provide concise, clear descriptions of job position
    2. Highlight key responsibilities and requirements
    3. Explain the role importance in the organization
    4. Keep descriptions brief and informative
    5. Focus on the most critical aspects of the position

    IMPORTANT: Your output MUST ALWAYS be formatted as a valid JSON array of objects.
    Each job description should follow this exact structure:
    
    ```json
    [
        {
            "job_title": "Title of the position",
            "Description": "Brief 1-2 sentence overview of the role",
            "key_responsibilities": [
                {
                    "responsibility": "Core responsibility 1",
                    "importance": "High/Medium/Low"
                },
                {
                    "responsibility": "Core responsibility 2", 
                    "importance": "High/Medium/Low"
                }
            ],
            "essential_requirements": [
                "Key requirement 1",
                "Key requirement 2"
            ],
            "preferred_qualifications": [
                "Preferred qualification 1",
                "Preferred qualification 2"
            ],
            "organizational_impact": {
                "team_contribution": "How this role supports the team",
                "business_value": "How this role creates value for the organization",
                "key_collaborations": ["Role 1", "Role 2"]
            },
            "career_path": {
                "reports_to": "Manager title",
                "growth_opportunities": ["Potential next step 1", "Potential next step 2"]
            }
        }
    ]
    """,

    llm_config=llm_config
)# To generate job description

agent_job_corrector = AssistantAgent(
    name="Job_Description_Corrector",
    system_message="""
    You are a job description corrector.
    Your role is to:
    1. Ensure accuracy and factual correctness of the job description.
    2. Correct grammar, spelling, or structural errors in the job description provided.
    3. Ensure clarity, professionalism, and conciseness.
    4. Keep the original intent and content intact.
    5. Do not add new content or commentary.
    6. Return only the corrected job description without any additional message, explanation, or prefix.
    
    IMPORTANT: Your output MUST ALWAYS be formatted as a valid JSON object.
    Each corrected job description should follow this exact structure:
    
    ```json
    {
        "correction_summary": {
            "errors_fixed": 5
        },
        "corrected_description": {
            "job_title": "Corrected job title",
            "description": "Corrected summary of the role",
            "responsibilities": [
                "Corrected responsibility 1",
                "Corrected responsibility 2"
            ],
            "requirements": [
                "Corrected requirement 1",
                "Corrected requirement 2"
            ],
            "qualifications": [
                "Corrected qualification 1",
                "Corrected qualification 2"
            ],
            "factual_corrections": [
                {
                    "original": "Original inaccurate text",
                    "corrected": "Corrected accurate text",
                    "justification": "Brief explanation of correction"
                }
            ]
        }
    }
    ```
    """,

    llm_config=llm_config
)# To correct job description if needed

agent_task_extractor = AssistantAgent(
    name="Task_Extractor",
    system_message="""
    You are an expert in task extraction from job descriptions.
    Your role is to:
    1. Extract all tasks from job descriptions
    2. Estimate time allocation percentages for each task
    3. Identify task dependencies and relationships
    4. Categorize tasks by functional area
    
    IMPORTANT: Your output MUST ALWAYS be formatted as a valid JSON array of objects.
    Each task extraction should follow this exact structure:
    
    ```json
    [
        {
            "task_name": "Task description",
            "time_percentage": 25 percentage,
            "effort_level": "High/Medium/Low",
            "task_category": "Category (e.g., Administrative, Analytical, Creative)",
            "description": "Detailed description of the task",
            "dependencies": ["Dependent task 1", "Dependent task 2"],
            "frequency": "Daily/Weekly/Monthly/As needed"
        }
    ]
    """,
    llm_config=llm_config
)# To extract tasks from job descriptions

agent_content_validator = AssistantAgent(
    name="Content_Validator",
    system_message="""
    You are an expert in content validation and quality assurance.
    Your role is to:
    1. Validate content accuracy and completeness
    2. Ensure proper HTML formatting
    3. Check for consistency and clarity
    4. Verify technical feasibility
    
    IMPORTANT: Your output MUST ALWAYS be formatted as a valid JSON object.
    Each content validation analysis should follow this exact structure:

    ```json
    {
        "validation_result":{
            "is_valid": true,
            "errors": [
                {
                    "line": 24,
                    "error": "Description of HTML error",
                    "fix": "Suggested correction"
                }
            ]
        }
    }
    ```
    """,

    llm_config=llm_config
)# To validate content

agent_ai_impact_analyzer = AssistantAgent(
    name="AI_Impact_Analyzer",
    system_message="""
    You are an expert in AI transformation and impact analysis.
    Your role is to:
    1. Analyze AI impact on job positions, clearly highlighting the differences between traditional tasks and AI-enhanced tasks. This includes detailing how tasks change, what new skills might be needed, and what aspects of the traditional role are diminished or augmented by AI.
    2. Evaluate task effort and time management, providing specific time estimates (e.g., hours per week, or percentage of total work time) for each task in both its traditional and AI-enhanced state. Ensure these estimates are realistic and clearly show the time saved or reallocated due to AI.
    3. Calculate productivity improvements based on these time estimates, quantifying the overall efficiency gain.
    4. Assess implementation complexity for the AI solutions proposed for each task.
    5. Calculate and include the total time percentage for tasks in their current state and the total time percentage for tasks after AI enhancement, summarizing the shift in workload.
    
    IMPORTANT: Your output MUST ALWAYS be formatted as a valid JSON array of objects.
    Each AI impact analysis should follow this exact structure:
    
    ```json
    [
        {
            "job_title": "Title of the position",
            "current_state": {
                "tasks": [
                    {
                        "task_name": "Task 1",
                        "time_percentage": "25 percentage",
                        "effort_level": "High/Medium/Low"
                    },
                    {
                        "task_name": "Task 2",
                        "time_percentage": "40 percentage",
                        "effort_level": "High/Medium/Low"
                    }
                ],
                "total_traditional_time_percentage": "65 percentage" // Sum of time_percentage from current_state.tasks
            },
            "ai_impact": {
                "automation_potential": {
                    "percentage": 65,
                    "description": "Description of how AI can automate parts of the role"
                },
                "task_changes": [
                    {
                        "task_name": "Task 1",
                        "new_time_percentage": "10 percentage",
                        "time_saved": "15 percentage"
                    },
                    {
                        "task_name": "Task 2",
                        "new_time_percentage": "20 percentage",
                        "time_saved": "20 percentage"
                    }
                ],
                "total_ai_enhanced_time_percentage": "30 percentage" // Sum of new_time_percentage from ai_impact.task_changes
            },
            "productivity_improvements": {
                "overall_productivity_gain": "35 percentage", // Calculated as (total_traditional_time_percentage - total_ai_enhanced_time_percentage)
                "quality_improvements": "Description of quality improvements",
                "new_capabilities": ["Capability 1", "Capability 2"]
            },
            "implementation": {
                "complexity": "High/Medium/Low",
                "estimated_timeline": "0-12 months / weeks",
                "key_challenges": ["Challenge 1", "Challenge 2"]
            }
        }
    ]
    ```
    Ensure all percentage values are strings ending with ' percentage'.
    """,
    
    llm_config=llm_config
)# To analyze AI impact

agent_ai_impact_assessor = AssistantAgent(
    name="AI_Impact_Assessor",
    system_message="""
    You are an expert in assessing AI impact on job tasks.
    Your role is to:
    1. Analyze automation potential for each task
    2. Calculate productivity improvements
    3. Assess implementation complexity
    4. Recommend AI tools and solutions
    5. Identify new AI-enabled capabilities
    
    IMPORTANT: Your output MUST ALWAYS be formatted as a valid JSON array of objects.
    Each AI impact assessment should follow this exact structure:
    
    ```json
    [
        {
            "task_name": "Task description",
            "automation_potential": {
                "percentage": 65,
                "category": "FULL/PARTIAL/NONE",
                "explanation": "Why this task can/cannot be automated"
            },
            "recommended_tools": ["Tool 1", "Tool 2"],
            "time_savings": {
                "current_time": "25 percentage",
                "projected_time": "10 percentage",
                "savings": "15 percentage"
            },
            "implementation": {
                "complexity": "High/Medium/Low",
                "timeline": "0-3/3-6/6-12 months",
                "dependencies": ["Dependency 1", "Dependency 2"]
            },
            "new_capabilities": ["New capability 1", "New capability 2"]
        }
    ]
    ```
    """,
    llm_config=llm_config
)# To analyze automation for each task

agent_task_automation_categorizer = AssistantAgent(
    name="Task_Automation_Categorizer",
    system_message="""
    You are an expert in AI automation analysis. Your role is to categorize job tasks based on automation potential.
    
    Your task is to:
    1. Analyze each job task and determine if it can be:
       a) FULLY AUTOMATED by AI (90-100 percentage automation)
       b) PARTIALLY AUTOMATED by AI (30-89 percentage automation)
       c) NOT AUTOMATABLE (0-29 percentage automation)
    2. Provide a detailed explanation for each categorization
    3. Pay special attention to tasks that CANNOT be automated
    4. Estimate the percentage of AI involvement for partially automated tasks
    
    IMPORTANT: Your output MUST ALWAYS be formatted as a valid JSON array of objects, ensuring all specified fields are populated. Do NOT include the "job_title" field.
    Each task automation analysis should follow this exact structure:
    
    ```json
    {
        "automation_analysis": [
            {
                "task": "Task description",
                "time_allocation": "25 percentage",
                "automation_category": "FULLY_AUTOMATED",
                "ai_involvement_percentage": 100,
                "explanation": "Detailed explanation of why this task can be fully automated",
                "recommended_ai_tools": ["Tool 1", "Tool 2"]
            },
            {
                "task": "Task description",
                "time_allocation": "30 percentage",
                "automation_category": "PARTIALLY_AUTOMATED",
                "ai_involvement_percentage": 70,
                "explanation": "Detailed explanation of why this task can be partially automated",
                "human_involvement_details": "Description of what the human still needs to do",
                "recommended_ai_tools": ["Tool 1", "Tool 2"]
            },
            {
                "task": "Task description",
                "time_allocation": "45 percentage",
                "automation_category": "NOT_AUTOMATABLE",
                "ai_involvement_percentage": 15,
                "explanation": "Detailed explanation of why this task cannot be automated",
                "human_critical_factors": ["Factor 1", "Factor 2"],
                "ai_assistance_possibilities": ["Limited assistance 1", "Limited assistance 2"]
            }
        ],
        "automation_summary": {
            "fully_automated_percentage": 25,
            "partially_automated_percentage": 30,
            "not_automatable_percentage": 45,
            "overall_ai_impact": "High/Medium/Low",
            "time_savings_estimate": "X hours per week"
        }
    }
    ```
    """,
    llm_config=llm_config
)# To Categorize the tasks for automation(full automation,partial automation,no automation)

agent_summary_writer = AssistantAgent(
    name="Summary_Writer",
    system_message="""
    You are an expert in creating concise executive plans.
    Your role is to:
    1. Synthesize information from multiple sources (job description, AI analysis, task changes, estimated time ...).
    2. Produce a brief, clear, and insightful executive plan as a plain text string.
    3. Focus on (percentage, saved time, proposed AI tools, sustainability and new opportunities)
    IMPORTANT: Your output MUST be a single, coherent paragraph of text. Do NOT use JSON or any other structured format.
    """,
    llm_config=llm_config
)# To write a summary of the analysis

html_generator = AssistantAgent(
    name="HTML_Generator",
    system_message="""
    You are an expert HTML visualization generator. Your role is to create clean, responsive HTML reports.
    1. Use modern HTML5, CSS3, and minimal JavaScript
    2. Create responsive, clean, professional designs
    3. Include appropriate data visualizations when relevant (e.g., charts showing time savings).
    4. If visualization file paths are provided (e.g., 'ai_gap_chart_path'), include them in the HTML as images using an <img> tag with a relative path if the image is saved in a static/images folder, or embed using a data URI if the image is passed as bytes.
    5. Ensure all content is properly formatted and accessible
    6. Return only the HTML content, no explanations or additional text
    
    Example for including a chart image if a path is provided:
    If you receive `ai_gap_chart_path: "charts/ai_gap_analysis.png"` in the input data,
    you should include something like this in your HTML:
    `<h2>AI Impact on Task Time Distribution</h2><img src="charts/ai_gap_analysis.png" alt="AI Gap Analysis Chart" style="width:100 percentage; max-width:600px;">`
    """,

    llm_config=llm_config
)# To generate HTML report

agent_ai_impact_assessor = AssistantAgent(
    name="AI_Impact_Assessor",
    system_message="""
    You are an expert in assessing AI impact on job tasks.
    Your role is to:
    1. Analyze automation potential for each task
    2. Calculate productivity improvements
    3. Assess implementation complexity
    4. Recommend AI tools and solutions
    5. Identify new AI-enabled capabilities
    
    IMPORTANT: Your output MUST ALWAYS be formatted as a valid JSON array of objects.
    Each AI impact assessment should follow this exact structure:
    
    ```json
    [
        {
            "task_name": "Task description",
            "automation_potential": {
                "percentage": 65,
                "category": "FULL/PARTIAL/NONE",
                "explanation": "Why this task can/cannot be automated"
            },
            "recommended_tools": ["Tool 1", "Tool 2"],
            "time_savings": {
                "current_time": 25 percentage,
                "projected_time": 10 percentage,
                "savings": 15 percentage
            },
            "implementation": {
                "complexity": "High/Medium/Low",
                "timeline": "0-3/3-6/6-12 months",
                "dependencies": ["Dependency 1", "Dependency 2"]
            },
            "new_capabilities": ["New capability 1", "New capability 2"]
        }
    ]
    """,
    llm_config=llm_config
)# To analyze automation for each task

agent_task_automation_categorizer = AssistantAgent(
    name="Task_Automation_Categorizer",
    system_message="""
    You are an expert in AI automation analysis. Your role is to categorize job tasks based on automation potential.
    
    Your task is to:
    1. Analyze each job task and determine if it can be:
       a) FULLY AUTOMATED by AI (90-100 percentage automation)
       b) PARTIALLY AUTOMATED by AI (30-89 percentage automation)
       c) NOT AUTOMATABLE (0-29 percentage automation)
    2. Provide a detailed explanation for each categorization
    3. Pay special attention to tasks that CANNOT be automated
    4. Estimate the percentage of AI involvement for partially automated tasks
    
    IMPORTANT: Your output MUST ALWAYS be formatted as a valid JSON array of objects, ensuring all specified fields are populated. The `ai_involvement_percentage` MUST be an integer. Do NOT include the "job_title" field.
    Each task automation analysis should follow this exact structure:
    
    ```json
    {
        "automation_analysis": [
            {
                "task": "Task description",
                "time_allocation": "25 percentage",
                "automation_category": "FULLY_AUTOMATED",
                "ai_involvement_percentage": 100,
                "explanation": "Detailed explanation of why this task can be fully automated",
                "recommended_ai_tools": ["Tool 1", "Tool 2"]
            },
            {
                "task": "Task description",
                "time_allocation": "30 percentage",
                "automation_category": "PARTIALLY_AUTOMATED",
                "ai_involvement_percentage": 70,
                "explanation": "Detailed explanation of why this task can be partially automated",
                "human_involvement_details": "Description of what the human still needs to do",
                "recommended_ai_tools": ["Tool 1", "Tool 2"]
            },
            {
                "task": "Task description",
                "time_allocation": "45 percentage",
                "automation_category": "NOT_AUTOMATABLE",
                "ai_involvement_percentage": 15,
                "explanation": "Detailed explanation of why this task cannot be automated",
                "human_critical_factors": ["Factor 1", "Factor 2"],
                "ai_assistance_possibilities": ["Limited assistance 1", "Limited assistance 2"]
            }
        ],
        "automation_summary": {
            "fully_automated_percentage": 25,
            "partially_automated_percentage": 30,
            "not_automatable_percentage": 45,
            "overall_ai_impact": "High/Medium/Low",
            "time_savings_estimate": "X hours per week"
        }
    }
    ```
    """,
    llm_config=llm_config
)# To Categorize the tasks for automation(full automation,partial automation,no automation)

agent_summary_writer = AssistantAgent(
    name="Summary_Writer",
    system_message="""
    You are an expert in creating concise executive summaries.
    Your role is to:
    1. Synthesize information from multiple sources (job description, AI analysis, task changes).
    2. Produce a brief, clear, and insightful executive summary as a plain text string.
    3. Focus on the key takeaways and overall impact.
    IMPORTANT: Your output MUST be a single, coherent paragraph of text. Do NOT use JSON or any other structured format.
    """,
    llm_config=llm_config
)# To write a summary of the analysis

html_generator = AssistantAgent(
    name="HTML_Generator",
    system_message="""
    You are an expert HTML visualization generator. Your role is to create clean, responsive HTML reports.
    1. Use modern HTML5, CSS3, and minimal JavaScript.
    2. Create responsive, clean, professional designs.
    3. Include appropriate data visualizations when relevant (e.g., charts showing time savings).
    4. If a chart image path is provided (e.g., in a field named `ai_gap_chart_path`), you MUST embed this image into the HTML. Use an `<img>` tag. For example, if `ai_gap_chart_path` is `charts/ai_gap_analysis.png`, include `<img src="charts/ai_gap_analysis.png" alt="AI Gap Analysis Chart" style="width:100 percentage; max-width:600px;">`. Ensure the src path is relative if the image is saved in a known static directory, or consider if a data URI is more appropriate if the image bytes are directly available.
    5. Ensure all content is properly formatted and accessible.
    6. Return only the HTML content, no explanations or additional text.
    """,

    llm_config=llm_config
)# To generate HTML report

'''

code_generator = AssistantAgent(
    name="Code_Generator",
    system_message="""
    You are an expert code generator. Your role is to write code for .
    1. Use Python libraries for chart generation
    2. Create simple, clean, and understandable code
    3. Include comments in the code to explain each part""",

    llm_config=llm_config
)# To generate code

code_corrector = AssistantAgent(
    name="Code_Corrector",
    system_message="""
    You are an expert code corrector. Your role is to correct the code generated by the code generator.
    1. Correct syntax errors
    2. Correct logical errors
    3. Correct spelling errors
    4. Correct grammar errors
    5. Correct punctuation errors
    6. Correct indentation errors
    7. Correct naming errors""",

    llm_config=llm_config
)# To correct code

code_exector = AssistantAgent(
    name="Code_Executor",
    system_message="""
    You are an expert code executor. Your role is to execute the code generated by the code generator.
    1. Execute the code
    2. Return the output
    3. If the code has errors, return the errors""",

    llm_config=llm_config
)# To execute code
'''

agent_job_analyzer = AssistantAgent(
    name="Job_Analyzer",
    system_message="""
    You are an expert in job analysis and description generation, tasked with providing in-depth insights.
    Your role is to:
    1. Generate comprehensive and detailed job descriptions.
    2. Validate and correct job descriptions for accuracy and completeness.
    3. Extract key insights from job requirements, going beyond surface-level details.
    4. Ensure high content quality, accuracy, and depth of analysis.
    5. Analyze the strategic importance of the role within an organization.
    6. Detail the problem-solving aspects and typical challenges encountered in this role.
    7. Provide insights into the competitive landscape and future trends affecting this role.
    
    IMPORTANT: Your output MUST ALWAYS be formatted as a valid JSON array of objects. 
    Each job analysis should follow this exact structure:
    
    ```json
    [
        {
            "job_title": "Title of the position",
            "job_summary": "Brief overview of the role, highlighting its core purpose.",
            "key_responsibilities": ["Detailed Responsibility 1", "Detailed Responsibility 2", ...],
            "required_qualifications": ["Specific Qualification 1", "Specific Qualification 2", ...],
            "preferred_qualifications": ["Specific Preferred Qualification 1", "Specific Preferred Qualification 2", ...],
            "skills_required": {
                "technical_skills": ["Technical Skill 1", "Technical Skill 2", ...],
                "soft_skills": ["Soft Skill 1", "Soft Skill 2", ...]
            },
            "strategic_importance": "In-depth analysis of the role's strategic value and contribution to organizational goals.",
            "problem_solving_challenges": "Description of typical problems solved, decision-making involved, and challenges faced in this role.",
            "industry_insights": "Analysis of how this role fits into the broader industry, including current demands and competitive factors.",
            "future_trends_impact": "Assessment of how emerging trends (e.g., technological advancements, market shifts, regulatory changes) are likely to impact this role and its required competencies."
        }
    ]    
    """,
    llm_config=llm_config
)# To analyze jobs

recommendation_agent = AssistantAgent(
    name="Recommendation_Agent",
    system_message="""
    You are an expert in generating recommendations based on analysis results.
    You are an expert in generating recommendations based on analysis results, including future skill development, sustainability planning, and opportunity identification.
    Your role is to:
    1. Generate general recommendations for AI implementation and role transition.
    2. Provide actionable insights for 'Future Skill Recommendations' needed to thrive in the AI-enhanced role.
    3. Develop a 'Sustainability Plan' outlining how AI can contribute to sustainable practices and environmental considerations within the job's context.
    4. Identify 'New Opportunities' created by AI adoption in this job function, such as new service offerings, market expansion, or innovation potential.
    5. Ensure all recommendations and plans are feasible, practical, and aligned with the overall AI transformation strategy.
    6. Prioritize recommendations based on impact and feasibility.
    7. Provide detailed implementation steps where applicable.

    IMPORTANT: Your output MUST ALWAYS be formatted as a valid JSON object containing separate arrays for general recommendations, future skills, sustainability plan, and new opportunities.
    The overall structure should be:

    ```json
    {
        "general_recommendations": [
            {
                "recommendation": "General recommendation text for AI adoption or role transition.",
                "rationale": "Why this recommendation is important for the business or individual.",
                "implementation_details": "Specific steps or considerations for implementing this recommendation.",
                "priority": "High/Medium/Low",
                "time_needed": "Estimated time to implement (e.g., 1-3 months, ongoing)",
                "related_new_capabilities": [] // Dynamically generate these based on the analysis
            }
        ],
        "future_skill_recommendations": [
            {
                "skill_area": "Data Literacy and Analysis",
                "specific_skills": ["Understanding AI model outputs", "Data-driven decision making", "Using analytics dashboards"],
                "learning_resources": ["Online course: AI for Everyone", "Company workshop: Data Analytics Basics"],
                "relevance_to_ai_role": "Essential for interpreting AI insights and guiding AI systems."
            }
        ],
        "sustainability_plan": [
            {
                "area": "Energy Efficiency in Operations",
                "initiative": "Utilize AI-powered scheduling for machinery to reduce idle time and energy consumption.",
                "impact_metric": "Potential 10 percentage reduction in energy usage for production line X.",
                "ai_tools_or_methods": ["AI-based optimization algorithms", "IoT sensor data for real-time monitoring"]
            },
            {
                "area": "Waste Reduction in Supply Chain",
                "initiative": "Implement AI for demand forecasting to minimize overproduction and material waste.",
                "impact_metric": "Target 5 percentage reduction in raw material spoilage.",
                "ai_tools_or_methods": ["Machine learning forecasting models", "Supply chain analytics platform"]
            }
        ],
        "opportunities_identification": [
            {
                "opportunity": "Personalized Customer Experience at Scale",
                "description": "Leverage AI to analyze customer data and provide highly personalized product recommendations or service interactions, improving customer satisfaction and sales.",
                "potential_value_proposition": "Increased customer retention by 15 percentage, Upsell/cross-sell revenue growth by 10 percentage.",
                "required_ai_capabilities": [] // Dynamically generate these based on the analysis
            },
            {
                "opportunity": "Predictive Maintenance for Equipment",
                "description": "Use AI to monitor equipment health in real-time and predict potential failures, allowing for proactive maintenance and minimizing downtime.",
                "potential_value_proposition": "Reduced equipment downtime by 20 percentage, Lowered maintenance costs by 15 percentage.",
                "required_ai_capabilities": [] // Dynamically generate these based on the analysis
            }
        ]
    }
    ```
    """,

    llm_config=llm_config
)# To generate recommendations based on analysis results

user_proxy = UserProxyAgent(
    name="User_Proxy",
    system_message="Execute tasks and coordinate between agents.",
    human_input_mode="NEVER",
    code_execution_config={"use_docker": False},
    max_consecutive_auto_reply=3,
    default_auto_reply="Please continue. If finished, reply 'TERMINATE'.",
    llm_config=llm_config
)

def get_job_description(job_position: str) -> Dict: # Generate a job description for the job position provided
    print(f"\n[INFO] Getting job description for: {job_position}")

    description_prompt = f"""
    Provide a comprehensive description of the job position: {job_position}
    Include:
    1. Overview of the position
    2. Key responsibilities and duties
    3. Required skills and qualifications
    4. Typical work environment
    5. Career progression opportunities
    """

    group_chat = GroupChat(
        agents=[agent_job_describer, agent_job_corrector],
        messages=[],
        max_round=2,
        speaker_selection_method="round_robin"
    )
    manager = GroupChatManager(groupchat=group_chat, llm_config=llm_config)

    user_proxy.initiate_chat(manager, message=description_prompt, silent=True)
    all_messages = manager.groupchat.messages

    job_description = all_messages[-1]["content"]
    
    try:
        json_content = json.loads(job_description)
        return {"content": job_description}
    except json.JSONDecodeError:
        return {"content": job_description}
    
def extract_tasks(job_description: Dict) -> list[Dict]: # Extract tasks from job description
    job_content = job_description["content"]
    print(f"\n[INFO] Extracting tasks from job description")
    
    extraction_prompt = f"""Extract all tasks from this job description, with detailed time allocations and categories: 
    
    {job_content}
    
    Be specific about each task and include realistic time percentages that sum to 100 percentage.
    """
    
    # Create a group chat for task extraction
    extraction_group = GroupChat(
        agents=[agent_task_extractor, agent_content_validator],
        messages=[],
        max_round=2
    )
    extraction_manager = GroupChatManager(groupchat=extraction_group, llm_config=llm_config)
    user_proxy.initiate_chat(extraction_manager, message=extraction_prompt, silent=True)
    
    extracted_tasks_content = extraction_manager.groupchat.messages[-1]["content"]
    extracted_tasks = parse_json_safely(extracted_tasks_content)
    
    if not extracted_tasks or not isinstance(extracted_tasks, list):
        print("[WARNING] Failed to extract tasks properly, using default structure")
        extracted_tasks = []
    
    return extracted_tasks

def analyze_ai_impact(job_description: Dict) -> Dict: # Analyze the AI impact on the job position provided
    job_content = job_description["content"]
    print(f"\n[INFO] Analyzing AI impact on job")
    
    extraction_prompt = f"""Extract all tasks from this job description: {job_content}"""
    extraction_group = GroupChat(
        agents=[agent_task_extractor, agent_content_validator],
        messages=[],
        max_round=2
    )
    extraction_manager = GroupChatManager(groupchat=extraction_group, llm_config=llm_config)
    user_proxy.initiate_chat(extraction_manager, message=extraction_prompt, silent=True)
    extracted_tasks = extraction_manager.groupchat.messages[-1]["content"]
    
    assessment_prompt = f"""Assess AI impact for these tasks: {extracted_tasks}"""
    assessment_group = GroupChat(
        agents=[agent_ai_impact_assessor, agent_content_validator],
        messages=[],
        max_round=3
    )
    assessment_manager = GroupChatManager(groupchat=assessment_group, llm_config=llm_config)
    user_proxy.initiate_chat(assessment_manager, message=assessment_prompt, silent=True)
    impact_assessment = assessment_manager.groupchat.messages[-1]["content"]
    
    return {
        "extracted_tasks": extracted_tasks,
        "impact_assessment": impact_assessment
    }

def assess_ai_impact(extracted_tasks: list[Dict]) -> list[Dict]: # Assess the AI impact on the extracted tasks
    print(f"\n[INFO] Assessing AI impact on tasks")
    
    if not extracted_tasks:
        print("[WARNING] No tasks to assess")
        return []
    
    assessment_prompt = f"""Assess the AI impact for each of these tasks:
    
    {json.dumps(extracted_tasks, indent=2)}
    
    For each task, analyze automation potential, recommended tools, and time savings.
    Be realistic about which tasks can be fully automated, partially automated, or not automated at all.
    """
    
    assessment_group = GroupChat(
        agents=[agent_ai_impact_assessor, agent_content_validator],
        messages=[],
        max_round=2
    )
    assessment_manager = GroupChatManager(groupchat=assessment_group, llm_config=llm_config)
    user_proxy.initiate_chat(assessment_manager, message=assessment_prompt, silent=True)
    
    impact_assessment_content = assessment_manager.groupchat.messages[-1]["content"]
    impact_assessment = parse_json_safely(impact_assessment_content)
    
    if not impact_assessment or not isinstance(impact_assessment, list):
        print("[WARNING] Failed to assess AI impact properly, using default structure")
        impact_assessment = []
    
    return impact_assessment

def categorize_tasks_by_automation(job_position: str, job_description: Dict, extracted_tasks: list[Dict]) -> Dict: # Categorize tasks by automation potential
    print(f"\n[INFO] Categorizing tasks by automation potential for: {job_position}")
    
    categorization_prompt = f"""
    For the position of {job_position}, categorize the following tasks based on their automation potential.
    
    Job Description:
    {job_description["content"]}
    
    Tasks with time allocations:
    {json.dumps(extracted_tasks, indent=2)}
    
    For each task, determine if it can be:
    1. FULLY AUTOMATED by AI (90-100 percentage automation)
    2. PARTIALLY AUTOMATED by AI (30-89 percentage automation)
    3. NOT AUTOMATABLE (0-29 percentage automation)
    
    Focus particularly on identifying and explaining tasks that CANNOT be automated and why human involvement remains critical.
    """
    
    # Create a group chat for task categorization
    categorization_group = GroupChat(
        agents=[agent_task_automation_categorizer, agent_content_validator],
        messages=[],
        max_round=2,
        speaker_selection_method="round_robin"
    )
    
    manager = GroupChatManager(groupchat=categorization_group, llm_config=llm_config)
    user_proxy.initiate_chat(
        manager,
        message=categorization_prompt,
        silent=True
    )
    all_messages = manager.groupchat.messages
    
    categorization_result = all_messages[-1]["content"]
    print(f"[DEBUG] Raw categorization_result from LLM: {categorization_result[:500]}...") # Log first 500 chars

    try:
        parsed_result = parse_json_safely(categorization_result)
        print(f"[DEBUG] Parsed categorization_result: {parsed_result}")

        if isinstance(parsed_result, dict):
            formatted_content = ""
            # Exclude job_title when formatting
            if "automation_summary" in parsed_result:
                summary = parsed_result["automation_summary"]
                formatted_content += "\n--- Automation Summary ---\n"
                for key, value in summary.items():
                    # Ensure job_title is not added here if it somehow slips through
                    if key != "job_title":
                        formatted_content += f"{key.replace('_', ' ').title()}: {value}\n"

            if "automation_analysis" in parsed_result and isinstance(parsed_result["automation_analysis"], list):
                formatted_content += "\n--- Task Analysis ---\n"
                for i, task_info in enumerate(parsed_result["automation_analysis"]):
                    formatted_content += f"\nTask {i+1}: {task_info.get('task', 'N/A')}\n"
                    formatted_content += f"  Time Allocation: {task_info.get('time_allocation', 'N/A')}\n"
                    formatted_content += f"  Automation Category: {task_info.get('automation_category', 'N/A')}\n"
                    formatted_content += f"  AI Involvement Percentage: {task_info.get('ai_involvement_percentage', 'N/A')}\n"
                    formatted_content += f"  Explanation: {task_info.get('explanation', 'N/A')}\n"
                    if task_info.get("human_involvement_details"):
                        formatted_content += f"  Human Involvement Details: {task_info['human_involvement_details']}\n"
                    if task_info.get("human_critical_factors"):
                        formatted_content += f"  Human Critical Factors: {task_info['human_critical_factors']}\n"
                    if task_info.get("recommended_ai_tools"):
                        formatted_content += f"  Recommended AI Tools: {task_info['recommended_ai_tools']}\n"
                    if task_info.get("ai_assistance_possibilities"):
                        formatted_content += f"  AI Assistance Possibilities: {task_info['ai_assistance_possibilities']}\n"
            
            print(f"[DEBUG] Formatted content being returned: {formatted_content[:500]}...") # Log formatted content
            return {"content": formatted_content, "parsed": parsed_result}
        else: # Fallback for non-dictionary parsed results
            print(f"[WARNING] Parsed automation categorization result is not a dictionary. Returning raw content.")
            return {"content": categorization_result, "parsed": parsed_result}
    except Exception as e:
        print(f"[ERROR] Failed to parse automation categorization result: {e}")
        return {"content": categorization_result, "parsed": {}} # Return empty dict on parsing failure

def extract_new_capabilities(impact_assessment: list[Dict]) -> list[str]: # Extract new skills when AI implemented in a job
    capabilities = []
    
    for task in impact_assessment:
        if (task.get("new_capabilities") and 
            isinstance(task["new_capabilities"], list) and 
            (task.get("ai_involvement_percentage", 0) > 70 or
             task.get("automation_potential", {}).get("category") == "FULL")):
            capabilities.extend(task["new_capabilities"])
    
    # If no capabilities found, generate some default ones
    if not capabilities and impact_assessment:
        return ["AI-assisted data analysis", "Automated reporting", "Intelligent decision support"]
    
    unique_capabilities = []
    for capability in capabilities:
        if capability not in unique_capabilities and capability:
            unique_capabilities.append(capability)
    
    return unique_capabilities

def extract_preserved_tasks(automation_analysis: list[Dict]) -> list[Dict]: # Extract preserved tasks 
    preserved_tasks = []
    
    for task in automation_analysis:
        # Check both automation_category and ai_involvement_percentage
        if (task.get("automation_category") == "NOT_AUTOMATABLE" or 
            task.get("automation_potential", {}).get("category") == "NONE" or
            task.get("ai_involvement_percentage", 0) < 30):
            preserved_tasks.append(task)
    
    # If no tasks match criteria, include at least one task as preserved
    if not preserved_tasks and automation_analysis:
        preserved_tasks.append(automation_analysis[0])
        
    return preserved_tasks

def extract_transformed_tasks(automation_analysis: list[Dict]) -> list[Dict]: # Extract transformed tasks
    transformed_tasks = []
    
    for task in automation_analysis:
        if (task.get("automation_category") == "PARTIALLY_AUTOMATED" or
            task.get("automation_potential", {}).get("category") == "PARTIAL" or
            (task.get("ai_involvement_percentage", 0) >= 30 and task.get("ai_involvement_percentage", 0) < 70)):
            transformed_tasks.append(task)
    
    if not transformed_tasks and automation_analysis:
        transformed_tasks.append(automation_analysis[1] if len(automation_analysis) > 1 else automation_analysis[0])
        
    return transformed_tasks

def analyze_ai_task_transformation(job_position: str) -> Dict: # Analyze AI task transformation
    print(f"\n[INFO] Analyzing AI task transformation for: {job_position}")

    try:
        job_description = get_job_description(job_position) # Get job description
        extracted_tasks = extract_tasks(job_description) # Extract tasks from job description
        impact_assessment = assess_ai_impact(extracted_tasks) # Evaluate AI impact on tasks
        automation_categorization = categorize_tasks_by_automation(job_position, job_description, extracted_tasks) # Categorize tasks by automation potential
        automation_analysis = automation_categorization.get("automation_analysis", []) # Generate transformed tasks (tasks that can be automated)
        
        transformed_tasks = extract_transformed_tasks(automation_analysis)
        preserved_tasks = extract_preserved_tasks(automation_analysis)
        new_capabilities = extract_new_capabilities(impact_assessment)
        
        # executive summary
        summary_prompt = f"""
        Create an executive summary for AI transformation of {job_position} based on:
        - Job Description: {job_description.get('content')}
        - Automation Analysis: {json.dumps(automation_categorization, indent=2)}
        - Transformed Tasks: {json.dumps(transformed_tasks, indent=2)}
        - Preserved Tasks: {json.dumps(preserved_tasks, indent=2)}
        - New Capabilities: {json.dumps(new_capabilities, indent=2)}
        
        Keep it concise and focused on key insights. Output a single paragraph of text.
        """
        
        summary_content = agent_summary_writer.generate_reply(
            messages=[{"role": "user", "content": summary_prompt}],
            sender=user_proxy
        )
        
        executive_summary_text = summary_content
        if isinstance(summary_content, dict) and "content" in summary_content:
            executive_summary_text = summary_content["content"]
        elif not isinstance(summary_content, str):
            executive_summary_text = str(summary_content) # Fallback
        
        # results
        return {
            "job_position": job_position,
            "job_description": job_description,
            "extracted_tasks": extracted_tasks,
            "impact_assessment": impact_assessment,
            "automation_categorization": automation_categorization,
            "transformed_tasks": transformed_tasks,
            "preserved_tasks": preserved_tasks,
            "new_capabilities": new_capabilities,
            "executive_summary": executive_summary_text,
            "timestamp": datetime.datetime.now().isoformat(),
            "status": "success"
        }
        
    except Exception as e:
        print(f"[ERROR] An error occurred during analysis: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "status": "error",
            "message": "An error occurred during analysis",
            "error": str(e)
        }
            
def validate_job_position(position: str) -> tuple[bool, str]: # Validate job 
    position = position.strip()

    if not position:
        return False, "Job position cannot be empty"
    if len(position) < 3:
        return False, "Job position must be at least 3 characters long"
    if len(position) > 100:
        return False, "Job position cannot exceed 100 characters"

    invalid_chars = set('!@#$%^&*()+=[]{}|\\:;"<>?/')
    if any(char in invalid_chars for char in position):
        return False, "Job position contains invalid special characters"
    if position.replace(" ", "").isnumeric():
        return False, "Job position cannot be numeric only"

    spam_patterns = ['www.', 'http', '.com', '.net', '.org']
    if any(pattern in position.lower() for pattern in spam_patterns):
        return False, "Job position cannot contain URLs or web addresses"

    return True, ""

def get_valid_job_position() -> str: # Get valid job position
    max_attempts = 2
    attempts = 0

    while attempts < max_attempts:
        position = input("\nEnter the job position to analyze: ").strip()
        is_valid, error_message = validate_job_position(position)

        if is_valid:
            description_prompt = f"""
            Provide a brief description of the job position: {position}
            Include:
            1. A one-sentence overview
            2. 2-3 key responsibilities
            3. Main required skills
            4. Typical work environment
            Keep the response concise and informative.
            """
            group_chat = GroupChat(
                agents=[agent_job_describer, agent_job_corrector],
                messages=[],
                max_round=3
            )
            manager = GroupChatManager(groupchat=group_chat, llm_config=llm_config)

            user_proxy.initiate_chat(manager, message=description_prompt, silent=True)
            all_messages = manager.groupchat.messages

            job_description = all_messages[-1]["content"]
            print("\n[INFO] Job Position Overview:")
            print("=" * 50)
            print(job_description)
            print("=" * 50)

            return position

        attempts += 1
        remaining = max_attempts - attempts
        print(f"\n[ERROR] {error_message}")
        if remaining > 0:
            print(f"Please try again. {remaining} attempts remaining.")

    raise ValueError("Maximum number of invalid input attempts reached. Please restart the program.")


def parse_json_safely(json_str): # Parse json file and handle errors
    try:
        return json.loads(json_str)
    except json.JSONDecodeError:
        try:
            return json.loads(json_str.replace("'", '"'))
        except json.JSONDecodeError:
            try:
                return parse_json_markdown(json_str)
            except Exception:
                return dirtyjson.loads(json_str)


def generate_ai_transition_recommendations(job_position: str, preserved_tasks: list[Dict], transformed_tasks: list[Dict], new_capabilities: list[str]) -> list[Dict]: # Generate recommendations 
    print(f"\n[INFO] Generating AI transition recommendations for: {job_position}")
    
    recommendation_prompt = f"""
    Generate specific recommendations for implementing AI and transitioning the role of {job_position}.
    
    Based on this analysis:
    
    1. Tasks that will remain human-driven:
    {json.dumps(preserved_tasks, indent=2)}
    
    2. Tasks that will be transformed by AI:
    {json.dumps(transformed_tasks, indent=2)}
    
    3. New capabilities enabled by AI:
    {json.dumps(new_capabilities, indent=2)}
    
    Provide actionable recommendations for:
    - Technology implementation
    - Training and skill development
    - Time management and allocation
    - Budget if needed
    - Process changes
    - Change management
    
    Each recommendation should be practical, specific, and prioritized.
    """
    
    recommendation_group = GroupChat(
        agents=[recommendation_agent, agent_content_validator],
        messages=[],
        max_round=2
    )
    recommendation_manager = GroupChatManager(groupchat=recommendation_group, llm_config=llm_config)
    user_proxy.initiate_chat(recommendation_manager, message=recommendation_prompt, silent=True)
    
    recommendations_content = recommendation_manager.groupchat.messages[-1]["content"]
    parsed_recommendations = parse_json_safely(recommendations_content)

    default_general_recommendation = [
        {
            "recommendation": f"Implement AI tools for {job_position}",
            "rationale": "To improve efficiency and productivity",
            "implementation_details": "Select appropriate AI tools and train staff",
            "priority": "High",
            "time_needed": "1-2 weeks",
            "related_new_capabilities": ["Basic AI tool usage"]
        }
    ]
    default_future_skills = [
        {
            "skill_area": "AI Literacy",
            "specific_skills": ["Understanding AI concepts", "Interacting with AI tools"],
            "learning_resources": ["Online introductory AI courses"],
            "relevance_to_ai_role": "Fundamental for working with new AI systems."
        }
    ]
    default_sustainability_plan = [
        {
            "area": "Digital Resource Optimization",
            "initiative": "Promote efficient use of AI tools to minimize computational overhead.",
            "impact_metric": "Reduced processing time for AI tasks.",
            "ai_tools_or_methods": ["Optimized AI models", "Efficient data handling"]
        }
    ]
    default_opportunities = [
        {
            "opportunity": "Enhanced Decision Making",
            "description": "Utilize AI insights to make more informed and timely decisions.",
            "potential_value_proposition": "Improved operational efficiency.",
            "required_ai_capabilities": ["Data analysis tools", "AI-driven reporting"]
        }
    ]

    if isinstance(parsed_recommendations, dict):
        recommendations = {
            "general_recommendations": parsed_recommendations.get("general_recommendations", default_general_recommendation),
            "future_skill_recommendations": parsed_recommendations.get("future_skill_recommendations", default_future_skills),
            "sustainability_plan": parsed_recommendations.get("sustainability_plan", default_sustainability_plan),
            "opportunities_identification": parsed_recommendations.get("opportunities_identification", default_opportunities)
        }
    else:
        print("[WARNING] Failed to generate recommendations properly or in expected dictionary format, using default structure for all recommendation types.")
        recommendations = {
            "general_recommendations": default_general_recommendation,
            "future_skill_recommendations": default_future_skills,
            "sustainability_plan": default_sustainability_plan,
            "opportunities_identification": default_opportunities
        }
    
    return recommendations

def analyze_job_for_ai_transformation(job_position: str) -> Dict: # Analyze job for AI transformation
    print(f"\n[INFO] Starting AI transformation analysis for: {job_position}")
    
    # Get job description
    job_description_raw = get_job_description(job_position)
    
    # Ensure job_description is a dictionary with a 'content' key
    job_description = {"content": "N/A"} # Default to prevent errors
    if isinstance(job_description_raw, dict):
        if "content" in job_description_raw and isinstance(job_description_raw["content"], str):
            try:
                # Attempt to parse the content if it looks like a JSON string
                parsed_content = json.loads(job_description_raw["content"])
                # If it's a list, take the first element; otherwise, use as is if it's a dict
                if isinstance(parsed_content, list) and len(parsed_content) > 0:
                    job_description = parsed_content[0]
                elif isinstance(parsed_content, dict):
                    job_description = parsed_content
                else:
                    # If parsed but not dict/list, treat raw content as the main content
                    job_description["content"] = job_description_raw["content"]
            except json.JSONDecodeError:
                # If not a valid JSON string, treat raw content as the main content
                job_description["content"] = job_description_raw["content"]
        else:
            # If job_description_raw is a dict but no 'content' or not a string, use it as is
            job_description = job_description_raw
            if "content" not in job_description:
                 job_description["content"] = json.dumps(job_description) # Fallback to dump to string if no 'content' key
    elif isinstance(job_description_raw, str):
        try:
            # If job_description_raw is a string, attempt to parse it as JSON
            parsed_content = json.loads(job_description_raw)
            if isinstance(parsed_content, list) and len(parsed_content) > 0:
                job_description = parsed_content[0]
            elif isinstance(parsed_content, dict):
                job_description = parsed_content
            else:
                job_description["content"] = job_description_raw # Fallback to string if not dict/list
        except json.JSONDecodeError:
            job_description["content"] = job_description_raw # Fallback to string if not JSON

    # Ensure the 'content' key is always present in job_description
    if "content" not in job_description:
        job_description["content"] = json.dumps(job_description) if isinstance(job_description, dict) else str(job_description_raw)

    print(f"[DEBUG] Final job_description before extract_tasks: {job_description.get('content', 'N/A')[:500]}...")
    # Extract tasks and pass the parsed job_description dictionary to extract_tasks
    # Ensure extract_tasks receives a dictionary with a 'content' key containing the job description text.
    extracted_tasks = extract_tasks({"content": job_description.get("content", "N/A")}) 
    # Safe string conversion for debugging
    extracted_tasks_str = str(extracted_tasks)
    print(f"[DEBUG] Extracted tasks: {extracted_tasks_str[:500]}...")

    # Assess AI impact
    impact_assessment = assess_ai_impact(extracted_tasks)
    impact_assessment_str = str(impact_assessment)
    print(f"[DEBUG] Impact assessment: {impact_assessment_str[:500]}...")

    # Categorize tasks and pass the parsed job_description dictionary to categorize_tasks_by_automation
    extracted_tasks_str = str(extracted_tasks)
    print(f"[DEBUG] Calling categorize_tasks_by_automation with job_position: {job_position}, job_description content: {job_description.get('content', 'N/A')[:500]}..., extracted_tasks: {extracted_tasks_str[:500]}...")
    automation_categorization = categorize_tasks_by_automation(job_position, job_description, extracted_tasks)
    automation_analysis = automation_categorization.get("automation_analysis", [])
    automation_categorization_str = str(automation_categorization)
    print(f"[DEBUG] Automation categorization result: {automation_categorization_str[:500]}...")

    preserved_tasks = extract_preserved_tasks(automation_analysis)
    transformed_tasks = extract_transformed_tasks(automation_analysis)
    new_capabilities = extract_new_capabilities(impact_assessment) 

    # Generate key ideas for traditional job
    traditional_job_summary = job_description.get("job_summary", job_description.get("Description", "N/A"))
    traditional_key_responsibilities = job_description.get("key_responsibilities", [])
    if isinstance(traditional_key_responsibilities, list) and traditional_key_responsibilities and isinstance(traditional_key_responsibilities[0], dict):
        traditional_key_responsibilities = [resp.get("responsibility", "N/A") for resp in traditional_key_responsibilities]
    
    key_ideas_traditional = {
        "summary": traditional_job_summary,
        "responsibilities": traditional_key_responsibilities[:3] 
    }

    ai_enhanced_summary_parts = [f"The role of {job_position} is poised for significant transformation through AI integration."]
    
    if new_capabilities:
        ai_enhanced_summary_parts.append(f"New capabilities such as {', '.join(new_capabilities[:2])} will emerge, allowing for enhanced strategic focus.")
    
    if transformed_tasks:
        ai_enhanced_summary_parts.append("Routine activities will be increasingly automated or augmented by AI, freeing up time for higher-value work.")
    else:
        ai_enhanced_summary_parts.append("AI will primarily assist in optimizing existing workflows and providing decision support.")

    # Highlight changes by comparing with traditional aspects
    if impact_assessment and isinstance(impact_assessment, list) and impact_assessment:
        first_impact = impact_assessment[0]
        if first_impact.get("ai_impact", {}).get("automation_potential", {}).get("percentage", 0) > 50:
            ai_enhanced_summary_parts.append("This represents a major shift from traditional methods, with over half of the role potentially benefiting from automation.")
        elif first_impact.get("ai_impact", {}).get("automation_potential", {}).get("percentage", 0) > 20:
            ai_enhanced_summary_parts.append("This marks a notable evolution from the traditional role, with significant AI assistance.")

    ai_enhanced_summary = " ".join(ai_enhanced_summary_parts)

    ai_enhanced_responsibilities = []
    # Dynamically list new/changed responsibilities based on analysis
    if transformed_tasks:
        for task_info in transformed_tasks[:2]: 
            task_name = task_info.get("task", task_info.get("task_name", "A key task"))
            ai_enhanced_responsibilities.append(f"{task_name} (Enhanced by AI)")
    
    if new_capabilities:
        for cap in new_capabilities[:1]: # Top 1 new capability as a responsibility
            ai_enhanced_responsibilities.append(f"Leveraging {cap} for strategic outcomes")

    if not ai_enhanced_responsibilities and preserved_tasks:
         # Fallback if no transformed tasks or new capabilities define responsibilities
        task_name = preserved_tasks[0].get("task", preserved_tasks[0].get("task_name", "A core human-centric task"))
        ai_enhanced_responsibilities.append(f"Overseeing {task_name} (Human-centric with AI support)") 
    elif not ai_enhanced_responsibilities:
        ai_enhanced_responsibilities.append("Strategic oversight of AI-augmented workflows and decision-making.")

    key_ideas_ai_enhanced = {
        "summary": ai_enhanced_summary,
        "responsibilities": list(set(ai_enhanced_responsibilities[:3])) # max 3
    }

    # Generate recommendations
    recommendations = generate_ai_transition_recommendations(
        job_position, 
        preserved_tasks, 
        transformed_tasks, 
        new_capabilities
    )

    general_recommendations = recommendations.get("general_recommendations", [])
    sustainability_plan = recommendations.get("sustainability_plan", [])
    opportunities_identification = recommendations.get("opportunities_identification", [])
    future_skill_recommendations = recommendations.get("future_skill_recommendations", [])

    # Return comprehensive results, including new sections
    return {
        "job_position": job_position,
        "job_description": job_description,
        "extracted_tasks": extracted_tasks, # Ensure this includes time_needed per task
        "impact_assessment": impact_assessment, # Ensure this highlights changes and includes time_needed
        "automation_categorization": automation_categorization,
        "preserved_tasks": preserved_tasks,
        "transformed_tasks": transformed_tasks,
        "new_capabilities": new_capabilities,
        "key_ideas_traditional": key_ideas_traditional,
        "key_ideas_ai_enhanced": key_ideas_ai_enhanced, 
        "general_recommendations": general_recommendations,
        "future_skill_recommendations": future_skill_recommendations,
        "sustainability_plan": sustainability_plan,
        "opportunities_identification": opportunities_identification,
        "timestamp": datetime.datetime.now().isoformat(),
        "status": "success"
    }

if __name__ == "__main__":    
    if len(sys.argv) > 1 and sys.argv[1] == "--api":
        print("\nStarting AI Job Transformation API Server")
        print("=" * 50)
        uvicorn.run(app, host="0.0.0.0", port=8000)
    else:
        print("\nAI Impact Analysis Tool")
        print("=" * 50)

        try:
            position = get_valid_job_position()
            print(f"\n[INFO] Analyzing position: {position}")

            transformation_results = analyze_job_for_ai_transformation(position)

            if transformation_results.get("status") == "error":
                print(f"\n[ERROR] Analysis failed: {transformation_results.get('message')}")
                print(f"Details: {transformation_results.get('error')}")
                exit(1)

            # Display results
            print("\nJob Description:")
            print(json.dumps(transformation_results["job_description"], indent=2))

            print("\nExtracted Tasks:")
            print(json.dumps(transformation_results["extracted_tasks"], indent=2))

            print("\nAI Impact Assessment:")
            print(json.dumps(transformation_results["impact_assessment"], indent=2))

            print("\nTransformed Tasks:")
            print(json.dumps(transformation_results["transformed_tasks"], indent=2))

            print("\nNew Capabilities:")
            print(json.dumps(transformation_results["new_capabilities"], indent=2))

            print("\nPreserved Tasks:")
            print(json.dumps(transformation_results["preserved_tasks"], indent=2))

        except ValueError as e:
            print(f"\n[ERROR] {str(e)}")
        except Exception as e:
            print(f"\n[ERROR] An unexpected error occurred: {str(e)}")
        finally:
            print("\n" + "=" * 50)
