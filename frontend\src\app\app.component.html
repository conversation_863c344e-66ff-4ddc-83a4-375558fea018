<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EDLIGO -Powered Workforce Analysis</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: Arial, sans-serif;
        }

        body {
            background-color: #f5f5f5;
            color: #333;
        }

        .app-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            background-color: #fff;
            padding: 10px 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: #333;
            text-decoration: none;
        }

        .nav-links a:hover {
            text-decoration: underline;
        }

        .hero {
            background-color: #007bff;
            color: #fff;
            padding: 100px 0;
            text-align: center;
        }

        .hero h1 {
            font-size: 36px;
            margin-bottom: 20px;
        }

        .hero p {
            font-size: 18px;
            margin-bottom: 30px;
        }

        .btn-group {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            color: #fff;
            cursor: pointer;
        }

        .btn-primary {
            background-color: #007bff;
        }

        .btn-secondary {
            background-color: #6c757d;
        }

        .features {
            background-color: #fff;
            padding: 50px 0;
        }

        .features h2 {
            font-size: 24px;
            margin-bottom: 20px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .feature-card {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            text-align: center;
        }

        .feature-icon {
            font-size: 48px;
            margin-bottom: 10px;
        }

        .feature-title {
            font-size: 20px;
            margin-bottom: 10px;
        }

        .feature-description {
            font-size: 16px;
        }

        footer {
            background-color: #333;
            color: #fff;
            padding: 30px 0;
        }

        .footer-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .footer-left {
            flex: 1;
        }

        .footer-right {
            flex: 1;
        }

        .footer-links {
            display: flex;
            gap: 20px;
        }

        .footer-links a {
            color: #fff;
            text-decoration: none;
        }

        .footer-links a:hover {
            text-decoration: underline;
        }

        .copyright {
            text-align: center;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header>
            <div class="container">
                <nav class="navbar">
                    <a routerLink="/" class="logo">
                        <img src="./assets/EDLIGO_TITLE.png" alt="Edligo AI" class="logo-img"
                             onerror="console.error('Failed to load image:', this.src); this.onerror=null;">
                    </a>
                    <div class="nav-links">
                        <a routerLink="/" routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}">Home</a>
                        <a routerLink="/analyze-job" routerLinkActive="active">Analyze Job</a>
                        <a routerLink="/about" routerLinkActive="active">About</a>
                    </div>
                </nav>
            </div>
        </header>

        <!-- Hero Section -->
        <section class="hero">
            <div class="container">
                <h1>AI-Powered Workforce Analysis</h1>
                <p>Understand how generative AI will impact job roles and workforce sustainability with our advanced analysis tool.</p>
                <div class="btn-group">
                    <a href="#" class="btn btn-primary">Analyze Your Job</a>
                    <a href="#" class="btn btn-secondary">Learn More</a>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="features">
            <div class="container">
                <h2>Our Analysis Features</h2>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">💡</div>
                        <h3 class="feature-title">AI Impact Analysis</h3>
                        <p class="feature-description">Get detailed insights on how generative AI might impact specific job roles and responsibilities.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📈</div>
                        <h3 class="feature-title">Future Skill Recommendations</h3>
                        <p class="feature-description">Receive recommendations for skills that will be valuable as AI continues to transform the workforce.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🛡️</div>
                        <h3 class="feature-title">Sustainability Planning</h3>
                        <p class="feature-description">Develop strategies for sustainable workforce development in the age of AI.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">⚡</div>
                        <h3 class="feature-title">Opportunity Identification</h3>
                        <p class="feature-description">Discover new opportunities created by the integration of AI in various industries.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer>
            <div class="container">
                <div class="footer-content">
                    <div class="footer-left">
                        <h3>Edigo AI</h3>
                        <p>Analyzing the impact of generative AI on workforce sustainability</p>
                    </div>
                    <div class="footer-right">
                        <h3>Quick Links</h3>
                        <div class="footer-links">
                            <a href="#">Home</a>
                            <a href="#">Analyze Job</a>
                            <a href="#">About</a>
                        </div>
                    </div>
                </div>
                <div class="copyright">
                    © 2025 Edigo AI. All rights reserved.
                </div>
            </div>
        </footer>
    </div>
</body>
</html>
