<div class="app-container">
  <!-- Header -->
  <header>
    <div class="container">
      <nav class="navbar">
        <a routerLink="/" class="logo">
          <img src="../../assets/EDLIGO_TITLE.png" alt="Edligo AI" class="logo-img" 
               onerror="console.error('Failed to load image:', this.src); this.onerror=null;">
        </a>
        <div class="nav-links">
          <a [routerLink]="['/']" routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}">Home</a>
          <a [routerLink]="['/analyze-job']" routerLinkActive="active">Analyze Job</a>
          <a [routerLink]="['/about']" routerLinkActive="active">About</a>
        </div>
      </nav>
      <script src="../main.js"></script>
    </div>
  </header>

  <!-- Main Content -->
  <main>
    <div class="analyze-job-container">
      <h1>Job Analysis</h1>
  <form (ngSubmit)="analyzeJob()" #analysisForm="ngForm">
    <div class="form-group">
      <label for="jobPosition">Job Position:</label>
      <input type="text" id="jobPosition" name="jobPosition" [(ngModel)]="jobPosition" required>
    </div>
    <button type="submit" [disabled]="isLoading || !analysisForm.form.valid">
      {{ isLoading ? 'Analyzing...' : 'Analyze Job' }}
    </button>
  </form>

  <div *ngIf="isLoading" class="loading-indicator">
    <div class="spinner"></div>
    <p>Loading analysis results...</p>
  </div>

  <div *ngIf="error" class="error-message">
    <p>Error: {{ error }}</p>
  </div>

  <!-- Recursive template to display nested objects and arrays -->
  <div *ngIf="analysisResult && !isLoading && !error && isObject(analysisResult)" class="results-display">
    <h2>Detailed Analysis:</h2>

    <ng-template #recursiveList let-data="data">
      <ul *ngIf="isObject(data)">
        <li *ngFor="let key of getObjectKeys(data)">
          <strong [class.special-key]="isSpecialKey(key)">{{ formatKeyName(key) }}:</strong>
          <!-- Default handling for all keys including automation_categorization -->
          <ng-container *ngIf="!shouldHideContent(key, data[key])">
            <ng-container *ngIf="isPrimitive(data[key])">
              {{ data[key] }}
            </ng-container>
            <ng-container *ngIf="isObject(data[key])">
              <ng-container *ngTemplateOutlet="recursiveList; context: { data: data[key] }"></ng-container>
            </ng-container>
            <ng-container *ngIf="isArray(data[key])">
              <ul class="nested-array">
                <li *ngFor="let item of data[key]; let i = index">
                  <ng-container *ngIf="isPrimitive(item)">
                    {{ item }}
                  </ng-container>
                  <ng-container *ngIf="isObject(item) || isArray(item)">
                    <ng-container *ngTemplateOutlet="recursiveList; context: { data: item }"></ng-container>
                  </ng-container>
                </li>
              </ul>
            </ng-container>
          </ng-container>
        </li>
      </ul>
      <div *ngIf="isArray(data)" class="array-display">
        <ul class="nested-array">
          <li *ngFor="let item of data; let i = index">
            <ng-container *ngIf="isPrimitive(item)">
              {{ item }}
            </ng-container>
            <ng-container *ngIf="isObject(item) || isArray(item)">
              <ng-container *ngTemplateOutlet="recursiveList; context: { data: item }"></ng-container>
            </ng-container>
          </li>
        </ul>
      </div>
    </ng-template>
    <ng-container *ngTemplateOutlet="recursiveList; context: { data: analysisResult }"></ng-container>

    </div> 
    </div> 
  </main>

  <!-- Footer -->
  <footer>
    <div class="container">
      <div class="footer-content">
        <div class="footer-left">
          <div class="footer-logo">
            <h3>Edligo</h3>
          </div>
          <div class="footer-tagline">
            <p>Analyzing the impact of generative AI on workforce sustainability</p>
          </div>
        </div>
        <div class="footer-right">
          <h3>Quick Links</h3>
          <div class="footer-links">
            <a [routerLink]="['/']">Home</a>
            <a [routerLink]="['/analyze-job']">Analyze Job</a>
            <a [routerLink]="['/about']">About</a>
          </div>
        </div>
      </div>
      <div class="copyright">© 2025 Edligo. All rights reserved.</div>
    </div>
  </footer>
</div>


