import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { ApiService } from '../services/api.service'; // Assuming ApiService path

@Component({
  selector: 'app-analyze-job',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule],
  templateUrl: './analyze-job.component.html',
  styleUrls: ['./analyze-job.component.css']
})
export class AnalyzeJobComponent {
  jobPosition: string = '';
  analysisResult: any = null;
  isLoading: boolean = false;
  error: string | null = null;

  constructor(private apiService: ApiService) {}

  analyzeJob() {
    if (!this.jobPosition.trim()) {
      this.error = 'Job position cannot be empty.';
      return;
    }
    this.isLoading = true;
    this.analysisResult = null;
    this.error = null;

    this.apiService.analyzeJob(this.jobPosition).subscribe({
      next: (data) => {
        this.analysisResult = data;
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Error fetching analysis:', err);
        this.error = `Failed to fetch analysis.`;
        // Attempt to display more specific error details if available
        if (err.error && err.error.detail) {
            this.error = `Analysis Error: ${err.error.detail}`;
        } else if (err.detail) {
            this.error = `Analysis Error: ${err.detail}`;
        } else if (err.message) {
            this.error = `Failed to fetch analysis: ${err.message}`;
        }
        this.isLoading = false;
      }
    });
  }

  // Helper to get keys from an object for iteration in the template
  getObjectKeys(obj: any): string[] {
    if (!obj) {
      return [];
    }
    const keysToExclude = ['backend_logs', 'status', 'timestamp', 'job_position'];
    return Object.keys(obj).filter(key => {
      const value = obj[key];
      if (keysToExclude.includes(key)) {
        return false;
      }
      // Exclude keys whose values are empty arrays or empty objects
      if (Array.isArray(value) && value.length === 0) {
        return false;
      }
      if (typeof value === 'object' && value !== null && Object.keys(value).length === 0) {
        return false;
      }
      return true;
    });
  }

  // Type guards for template
  isObject(value: any): boolean {
    return typeof value === 'object' && value !== null && !Array.isArray(value);
  }

  isArray(value: any): boolean {
    return Array.isArray(value);
  }

  isPrimitive(value: any): boolean {
    return typeof value !== 'object' || value === null;
  }

  formatKeyName(key: string): string {
    if (!key) return '';
    return key.replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase());
  }

  isSpecialKey(key: string): boolean {
    const specialKeys = ['job_description', 'key_responsibilities', 'job_title', 'company_overview', 'skills_required', 'experience_level', 'education_requirements', 'salary_range', 'benefits', 'location', 'employment_type']; // Add more keys as needed
    return specialKeys.includes(key.toLowerCase());
  }

  parseAutomationCategorization(value: string): any[] | null {
    if (typeof value === 'string') {
      try {
        let sanitizedValue = value.replace(/\bTrue\b/g, 'true')
                                .replace(/\bFalse\b/g, 'false')
                                .replace(/\bNone\b/g, 'null')
                                .replace(/'/g, '"'); 

        // Handle cases where the string might be a list of dicts or a dict of dicts
        // and ensure it's parsed into a consistent array format for the template.
        const parsed = JSON.parse(sanitizedValue);

        if (Array.isArray(parsed)) {
          return parsed; 
        } else if (typeof parsed === 'object' && parsed !== null) {
          return Object.values(parsed);
        }
        // If it's a primitive or other non-array/non-object type after parsing, return null or handle as error
        console.warn('Parsed automation_categorization is not an array or object:', parsed);
        return null;
      } catch (e) {
        console.error('Error parsing automation_categorization:', e, 'Original value:', value);
        if (value.includes('\n') && !value.trim().startsWith('{') && !value.trim().startsWith('[')) {
          return value.split('\n').map(item => item.trim()).filter(item => item.length > 0);
        }
        return null;
      }
    }
    return null;
  }

  downloadReport(): void {
    if (!this.analysisResult) {
      console.error('No analysis result to download.');
      this.error = 'No analysis result available to download.';
      return;
    }

    try {
      const jsonData = JSON.stringify(this.analysisResult, null, 2); // Pretty print JSON
      const blob = new Blob([jsonData], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      const jobPositionFilename = this.jobPosition.replace(/[^a-z0-9_\-\s]/gi, '_').replace(/\s+/g, '_');
      a.download = `${jobPositionFilename}_analysis_report.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (e) {
      console.error('Error preparing download:', e);
      this.error = 'Failed to prepare report for download.';
    }
  }
}