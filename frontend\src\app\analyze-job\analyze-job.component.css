/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: Arial, sans-serif;
}

body {
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Header Styles */
header {
    background-color: #fff;
    padding: 1rem 5%;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    text-decoration: none;
}

.logo-img {
    height: 40px;
    width: auto;
}

.nav-links a {
    margin-left: 20px;
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-links a:hover,
.nav-links a.active {
    color: #007bff;
}

/* Main Content Styles */
main {
    padding: 2rem 5%;
}

.analyze-job-container {
    background-color: #fff;
    padding: 25px; 
    border-radius: 10px; 
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1); 
    max-width: 850px; 
    margin: 2rem auto;
    border: 1px solid #dee2e6;
}

.analyze-job-container h1 {
    text-align: center;
    margin-bottom: 20px;
    color: #2c3e50;
    font-size: 2.2em;
    /* text-decoration: underline; */ /* Remove underline if global style handles title decoration */
    /* border-bottom: 2px solid #007bff; */ /* Handled by global style if applicable */
    /* display: inline-block; */
    /* padding-bottom: 10px; */
}

/* Results display enhancements from previous step can be kept or further integrated */
.results-display {
  margin-top: 25px;
  padding: 20px;
  background-color: #fdfdff; /* Very light background for results area */
  border: 1px solid #e9ecef;
  border-radius: 6px;
  box-shadow: inset 0 1px 3px rgba(0,0,0,0.05); /* Inner shadow for depth */
}

.results-display h2 {
  margin-bottom: 15px;
  color: #333;
  font-size: 1.5em;
  text-decoration: underline;
}

.results-display ul {
  list-style-type: none; 
  padding-left: 0; 
}

.results-display li {
  margin-bottom: 8px; 
  line-height: 1.6;
}

.results-display strong {
  color: #555; 
  margin-right: 5px;
  /* text-decoration: underline; */ /* Removed generic underline */
}

/* Styles for top-level list items to make them appear as cards/sections */
.results-display > ul > li {
  background-color: #f9f9f9; /* Light background for the card */
  border: 1px solid #e0e0e0; /* Border for the card */
  border-radius: 6px; /* Rounded corners */
  padding: 15px; /* Padding inside the card */
  margin-bottom: 15px; /* Space between cards */
  box-shadow: 0 2px 5px rgba(0,0,0,0.05); /* Subtle shadow */
}

/* Style for the strong tag (key name) within these top-level cards to act as a header */
.results-display > ul > li > strong {
  display: block; /* Make it a block element to take full width */
  font-size: 1.2em; /* Larger font size for the header */
  color: #333; /* Darker color for the header */
  margin-bottom: 10px; /* Space below the header */
  padding-bottom: 5px; /* Space before the border-bottom */
  border-bottom: 1px solid #eee; /* Separator line below the header */
  text-decoration: none; /* Ensure no underline from other rules */
}

/* Adjustments for nested lists within the new card structure */
.results-display > ul > li .nested-array,
.results-display > ul > li .array-display ul {
  padding-left: 15px; /* Adjust padding for nested lists if needed */
  border-left-width: 1px; /* Thinner border for nested lists */
  margin-top: 10px;
}

.results-display > ul > li .nested-array li,
.results-display > ul > li .array-display ul li {
  font-size: 0.9em; /* Slightly smaller font for nested items */
  margin-bottom: 5px;
}

/* Ensure strong tags within nested structures are not overly styled by the top-level header style */
.results-display > ul > li .nested-array strong,
.results-display > ul > li .array-display ul strong {
    display: inline; /* Keep nested strong tags inline */
    font-size: 1em; /* Reset font size if needed */
    color: #555;
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

/* Styles for nested lists (objects/arrays) - general styling */
.results-display .nested-array,
.results-display .array-display ul {
  margin-top: 5px;
  padding-left: 20px; 
  border-left: 2px solid #eee; 
}

.results-display .nested-array li,
.results-display .array-display ul li {
  font-size: 0.95em; 
}

/* Styles for automation_categorization tasks */
.automation-tasks {
  margin-top: 0.5rem;
  padding-left: 10px; 
}

.automation-tasks .task-item {
  padding: 0.75rem;
  margin-bottom: 1rem;
  background-color: #ffffff; 
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.automation-tasks .task-item p {
  margin-bottom: 0.3rem; 
  line-height: 1.5;
}

.automation-tasks .task-item p strong {
  color: #333; 
  margin-right: 5px;
  font-weight: bold; /* Ensure nested strong tags in tasks are bold but not like headers */
  font-size: 1em;
  display: inline;
  border-bottom: none;
}

/* Styles for special keys */
.special-key {
  color: #007bff; 
  font-weight: bold;
}

/* Styles for the horizontal rule */
.results-display hr {
  border: 0;
  height: 1px;
  background-color: #ccc; 
  margin: 15px 0; 
}

/* Download Button Styles */
.download-button-container {
  text-align: center; 
  margin-top: 20px; 
  margin-bottom: 20px; 
}

.download-button {
  background-color: #007bff; 
  color: white; 
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s ease;
}

.download-button:hover {
  background-color: #0056b3; 
}

.download-button:disabled {
  background-color: #cccccc;
  color: #666666;
  cursor: not-allowed;
}

/* Footer Styles from home.component.css */
footer {
  background-color: #fff;
  padding: 40px 0 20px;
  border-top: 1px solid #eee;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
}

.footer-left {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.footer-logo h3 {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin: 0;
  text-decoration: none; 
}

.footer-tagline p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.footer-right h3 {
  font-size: 18px;
  margin-bottom: 15px;
  color: #333;
  text-decoration: none; 
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.footer-links a {
  color: #666;
  text-decoration: none; 
  transition: color 0.3s;
}

.footer-links a:hover {
  color: #007bff;
  text-decoration: none; 
}

.copyright {
  text-align: center;
  color: #666;
  font-size: 14px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.footer-links a {
    display: block;
    color: #333;
    text-decoration: none;
    margin-bottom: 0.5rem;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #007bff;
}

.copyright {
    font-size: 0.9em;
    color: #333;
    border-top: 1px solid #ddd;
    padding-top: 1rem;
    margin-top: 1rem;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #34495e;
}

.form-group input[type="text"] {
    width: 100%;
    padding: 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1em;
}

button[type="submit"] {
    background-color: #007bff;
    color: white;
    padding: 12px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1em;
    transition: background-color 0.3s ease;
    display: block;
    width: 100%;
}

button[type="submit"]:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

button[type="submit"]:hover:not(:disabled) {
    background-color: #0056b3;
}

/* Loading and Error Styles */
.loading-indicator, .error-message {
    text-align: center;
    margin-top: 20px;
    padding: 15px;
    border-radius: 4px;
}

.loading-indicator {
    color: #007bff;
}

.error-message {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border-left-color: #007bff;
    animation: spin 1s ease infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Styles for the results display */
.results-display {
  margin-top: 20px;
  padding: 15px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.results-display h2 {
  margin-bottom: 15px;
  color: #333;
  font-size: 1.5em;
  text-decoration: underline;
}

.results-display ul {
  list-style-type: none; 
  padding-left: 0; 
}

.results-display li {
  margin-bottom: 8px; 
  line-height: 1.6;
}

.results-display strong {
  color: #555; 
  margin-right: 5px;
  /* text-decoration: underline; */ /* Removed generic underline */
}

/* Styles for top-level list items to make them appear as cards/sections */
.results-display > ul > li {
  background-color: #f9f9f9; /* Light background for the card */
  border: 1px solid #e0e0e0; /* Border for the card */
  border-radius: 6px; /* Rounded corners */
  padding: 15px; /* Padding inside the card */
  margin-bottom: 15px; /* Space between cards */
  box-shadow: 0 2px 5px rgba(0,0,0,0.05); /* Subtle shadow */
}

/* Style for the strong tag (key name) within these top-level cards to act as a header */
.results-display > ul > li > strong {
  display: block; /* Make it a block element to take full width */
  font-size: 1.2em; /* Larger font size for the header */
  color: #333; /* Darker color for the header */
  margin-bottom: 10px; /* Space below the header */
  padding-bottom: 5px; /* Space before the border-bottom */
  border-bottom: 1px solid #eee; /* Separator line below the header */
  text-decoration: none; /* Ensure no underline from other rules */
}

/* Adjustments for nested lists within the new card structure */
.results-display > ul > li .nested-array,
.results-display > ul > li .array-display ul {
  padding-left: 15px; /* Adjust padding for nested lists if needed */
  border-left-width: 1px; /* Thinner border for nested lists */
  margin-top: 10px;
}

.results-display > ul > li .nested-array li,
.results-display > ul > li .array-display ul li {
  font-size: 0.9em; /* Slightly smaller font for nested items */
  margin-bottom: 5px;
}

/* Ensure strong tags within nested structures are not overly styled by the top-level header style */
.results-display > ul > li .nested-array strong,
.results-display > ul > li .array-display ul strong {
    display: inline; /* Keep nested strong tags inline */
    font-size: 1em; /* Reset font size if needed */
    color: #555;
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

/* Styles for nested lists (objects/arrays) - general styling */
.results-display .nested-array,
.results-display .array-display ul {
  margin-top: 5px;
  padding-left: 20px; 
  border-left: 2px solid #eee; 
}

.results-display .nested-array li,
.results-display .array-display ul li {
  font-size: 0.95em; 
}

/* Styles for automation_categorization tasks */
.automation-tasks {
  margin-top: 0.5rem;
  padding-left: 10px; 
}

.automation-tasks .task-item {
  padding: 0.75rem;
  margin-bottom: 1rem;
  background-color: #ffffff; 
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.automation-tasks .task-item p {
  margin-bottom: 0.3rem; 
  line-height: 1.5;
}

.automation-tasks .task-item p strong {
  color: #333; 
  margin-right: 5px;
  font-weight: bold; /* Ensure nested strong tags in tasks are bold but not like headers */
  font-size: 1em;
  display: inline;
  border-bottom: none;
}

/* Styles for special keys */
.special-key {
  color: #007bff; 
  font-weight: bold;
}

/* Styles for the horizontal rule */
.results-display hr {
  border: 0;
  height: 1px;
  background-color: #ccc; 
  margin: 15px 0; 
}

/* Download Button Styles */
.download-button-container {
  text-align: center; 
  margin-top: 20px; 
  margin-bottom: 20px; 
}

.download-button {
  background-color: #007bff; 
  color: white; 
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s ease;
}

.download-button:hover {
  background-color: #0056b3; 
}

.download-button:disabled {
  background-color: #cccccc;
  color: #666666;
  cursor: not-allowed;
}

/* Footer Styles from home.component.css */
footer {
  background-color: #fff;
  padding: 40px 0 20px;
  border-top: 1px solid #eee;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
}

.footer-left {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.footer-logo h3 {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin: 0;
  text-decoration: none; 
}

.footer-tagline p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.footer-right h3 {
  font-size: 18px;
  margin-bottom: 15px;
  color: #333;
  text-decoration: none; 
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.footer-links a {
  color: #666;
  text-decoration: none; 
  transition: color 0.3s;
}

.footer-links a:hover {
  color: #007bff;
  text-decoration: none; 
}

.copyright {
  text-align: center;
  color: #666;
  font-size: 14px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.footer-links a {
    display: block;
    color: #333;
    text-decoration: none;
    margin-bottom: 0.5rem;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #007bff;
}

.copyright {
    font-size: 0.9em;
    color: #333;
    border-top: 1px solid #ddd;
    padding-top: 1rem;
    margin-top: 1rem;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #34495e;
}

.form-group input[type="text"] {
    width: 100%;
    padding: 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1em;
}

button[type="submit"] {
    background-color: #007bff;
    color: white;
    padding: 12px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1em;
    transition: background-color 0.3s ease;
    display: block;
    width: 100%;
}

button[type="submit"]:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

button[type="submit"]:hover:not(:disabled) {
    background-color: #0056b3;
}

/* Loading and Error Styles */
.loading-indicator, .error-message {
    text-align: center;
    margin-top: 20px;
    padding: 15px;
    border-radius: 4px;
}

.loading-indicator {
    color: #007bff;
}

.error-message {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border-left-color: #007bff;
    animation: spin 1s ease infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Styles for the results display */
.results-display {
  margin-top: 20px;
  padding: 15px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.results-display h2 {
  margin-bottom: 15px;
  color: #333;
  font-size: 1.5em;
  text-decoration: underline;
}

.results-display ul {
  list-style-type: none; 
  padding-left: 0; 
}

.results-display li {
  margin-bottom: 8px; 
  line-height: 1.6;
}

.results-display strong {
  color: #555; 
  margin-right: 5px;
  /* text-decoration: underline; */ /* Removed generic underline */
}

/* Styles for top-level list items to make them appear as cards/sections */
.results-display > ul > li {
  background-color: #f9f9f9; /* Light background for the card */
  border: 1px solid #e0e0e0; /* Border for the card */
  border-radius: 6px; /* Rounded corners */
  padding: 15px; /* Padding inside the card */
  margin-bottom: 15px; /* Space between cards */
  box-shadow: 0 2px 5px rgba(0,0,0,0.05); /* Subtle shadow */
}

/* Style for the strong tag (key name) within these top-level cards to act as a header */
.results-display > ul > li > strong {
  display: block; /* Make it a block element to take full width */
  font-size: 1.2em; /* Larger font size for the header */
  color: #333; /* Darker color for the header */
  margin-bottom: 10px; /* Space below the header */
  padding-bottom: 5px; /* Space before the border-bottom */
  border-bottom: 1px solid #eee; /* Separator line below the header */
  text-decoration: none; /* Ensure no underline from other rules */
}

/* Adjustments for nested lists within the new card structure */
.results-display > ul > li .nested-array,
.results-display > ul > li .array-display ul {
  padding-left: 15px; /* Adjust padding for nested lists if needed */
  border-left-width: 1px; /* Thinner border for nested lists */
  margin-top: 10px;
}

.results-display > ul > li .nested-array li,
.results-display > ul > li .array-display ul li {
  font-size: 0.9em; /* Slightly smaller font for nested items */
  margin-bottom: 5px;
}

/* Ensure strong tags within nested structures are not overly styled by the top-level header style */
.results-display > ul > li .nested-array strong,
.results-display > ul > li .array-display ul strong {
    display: inline; /* Keep nested strong tags inline */
    font-size: 1em; /* Reset font size if needed */
    color: #555;
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

/* Styles for nested lists (objects/arrays) - general styling */
.results-display .nested-array,
.results-display .array-display ul {
  margin-top: 5px;
  padding-left: 20px; 
  border-left: 2px solid #eee; 
}

.results-display .nested-array li,
.results-display .array-display ul li {
  font-size: 0.95em; 
}

/* Styles for automation_categorization tasks */
.automation-tasks {
  margin-top: 0.5rem;
  padding-left: 10px; 
}

.automation-tasks .task-item {
  padding: 0.75rem;
  margin-bottom: 1rem;
  background-color: #ffffff; 
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.automation-tasks .task-item p {
  margin-bottom: 0.3rem; 
  line-height: 1.5;
}

.automation-tasks .task-item p strong {
  color: #333; 
  margin-right: 5px;
  font-weight: bold; /* Ensure nested strong tags in tasks are bold but not like headers */
  font-size: 1em;
  display: inline;
  border-bottom: none;
}

/* Styles for special keys */
.special-key {
  color: #007bff; 
  font-weight: bold;
}

/* Styles for the horizontal rule */
.results-display hr {
  border: 0;
  height: 1px;
  background-color: #ccc; 
  margin: 15px 0; 
}

/* Download Button Styles */
.download-button-container {
  text-align: center; 
  margin-top: 20px; 
  margin-bottom: 20px; 
}

.download-button {
  background-color: #007bff; 
  color: white; 
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s ease;
}

.download-button:hover {
  background-color: #0056b3; 
}

.download-button:disabled {
  background-color: #cccccc;
  color: #666666;
  cursor: not-allowed;
}

/* Footer Styles from home.component.css */
footer {
  background-color: #fff;
  padding: 40px 0 20px;
  border-top: 1px solid #eee;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
}

.footer-left {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.footer-logo h3 {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin: 0;
  text-decoration: none; 
}

.footer-tagline p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.footer-right h3 {
  font-size: 18px;
  margin-bottom: 15px;
  color: #333;
  text-decoration: none; 
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.footer-links a {
  color: #666;
  text-decoration: none; 
  transition: color 0.3s;
}

.footer-links a:hover {
  color: #007bff;
  text-decoration: none; 
}

.copyright {
  text-align: center;
  color: #666;
  font-size: 14px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.footer-links a {
    display: block;
    color: #333;
    text-decoration: none;
    margin-bottom: 0.5rem;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #007bff;
}

.copyright {
    font-size: 0.9em;
    color: #333;
    border-top: 1px solid #ddd;
    padding-top: 1rem;
    margin-top: 1rem;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #34495e;
}

.form-group input[type="text"] {
    width: 100%;
    padding: 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1em;
}

button[type="submit"] {
    background-color: #007bff;
    color: white;
    padding: 12px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1em;
    transition: background-color 0.3s ease;
    display: block;
    width: 100%;
}

button[type="submit"]:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

button[type="submit"]:hover:not(:disabled) {
    background-color: #0056b3;
}

/* Loading and Error Styles */
.loading-indicator, .error-message {
    text-align: center;
    margin-top: 20px;
    padding: 15px;
    border-radius: 4px;
}

.loading-indicator {
    color: #007bff;
}

.error-message {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border-left-color: #007bff;
    animation: spin 1s ease infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Styles for the results display */
.results-display {
  margin-top: 20px;
  padding: 15px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.results-display h2 {
  margin-bottom: 15px;
  color: #333;
  font-size: 1.5em;
  text-decoration: underline;
}

.results-display ul {
  list-style-type: none; 
  padding-left: 0; 
}

.results-display li {
  margin-bottom: 8px; 
  line-height: 1.6;
}

.results-display strong {
  color: #555; 
  margin-right: 5px;
  /* text-decoration: underline; */ /* Removed generic underline */
}

/* Styles for top-level list items to make them appear as cards/sections */
.results-display > ul > li {
  background-color: #f9f9f9; /* Light background for the card */
  border: 1px solid #e0e0e0; /* Border for the card */
  border-radius: 6px; /* Rounded corners */
  padding: 15px; /* Padding inside the card */
  margin-bottom: 15px; /* Space between cards */
  box-shadow: 0 2px 5px rgba(0,0,0,0.05); /* Subtle shadow */
}

/* Style for the strong tag (key name) within these top-level cards to act as a header */
.results-display > ul > li > strong {
  display: block; /* Make it a block element to take full width */
  font-size: 1.2em; /* Larger font size for the header */
  color: #333; /* Darker color for the header */
  margin-bottom: 10px; /* Space below the header */
  padding-bottom: 5px; /* Space before the border-bottom */
  border-bottom: 1px solid #eee; /* Separator line below the header */
  text-decoration: none; /* Ensure no underline from other rules */
}

/* Adjustments for nested lists within the new card structure */
.results-display > ul > li .nested-array,
.results-display > ul > li .array-display ul {
  padding-left: 15px; /* Adjust padding for nested lists if needed */
  border-left-width: 1px; /* Thinner border for nested lists */
  margin-top: 10px;
}

.results-display > ul > li .nested-array li,
.results-display > ul > li .array-display ul li {
  font-size: 0.9em; /* Slightly smaller font for nested items */
  margin-bottom: 5px;
}

/* Ensure strong tags within nested structures are not overly styled by the top-level header style */
.results-display > ul > li .nested-array strong,
.results-display > ul > li .array-display ul strong {
    display: inline; /* Keep nested strong tags inline */
    font-size: 1em; /* Reset font size if needed */
    color: #555;
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

/* Styles for nested lists (objects/arrays) - general styling */
.results-display .nested-array,
.results-display .array-display ul {
  margin-top: 5px;
  padding-left: 20px; 
  border-left: 2px solid #eee; 
}

.results-display .nested-array li,
.results-display .array-display ul li {
  font-size: 0.95em; 
}

/* Styles for automation_categorization tasks */
.automation-tasks {
  margin-top: 0.5rem;
  padding-left: 10px; 
}

.automation-tasks .task-item {
  padding: 0.75rem;
  margin-bottom: 1rem;
  background-color: #ffffff; 
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.automation-tasks .task-item p {
  margin-bottom: 0.3rem; 
  line-height: 1.5;
}

.automation-tasks .task-item p strong {
  color: #333; 
  margin-right: 5px;
  font-weight: bold; /* Ensure nested strong tags in tasks are bold but not like headers */
  font-size: 1em;
  display: inline;
  border-bottom: none;
}

/* Styles for special keys */
.special-key {
  color: #007bff; 
  font-weight: bold;
}

/* Styles for the horizontal rule */
.results-display hr {
  border: 0;
  height: 1px;
  background-color: #ccc; 
  margin: 15px 0; 
}

/* Download Button Styles */
.download-button-container {
  text-align: center; 
  margin-top: 20px; 
  margin-bottom: 20px; 
}

.download-button {
  background-color: #007bff; 
  color: white; 
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s ease;
}

.download-button:hover {
  background-color: #0056b3; 
}

.download-button:disabled {
  background-color: #cccccc;
  color: #666666;
  cursor: not-allowed;
}

/* Footer Styles from home.component.css */
footer {
  background-color: #fff;
  padding: 40px 0 20px;
  border-top: 1px solid #eee;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
}

.footer-left {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.footer-logo h3 {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin: 0;
  text-decoration: none; 
}

.footer-tagline p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.footer-right h3 {
  font-size: 18px;
  margin-bottom: 15px;
  color: #333;
  text-decoration: none; 
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.footer-links a {
  color: #666;
  text-decoration: none; 
  transition: color 0.3s;
}

.footer-links a:hover {
  color: #007bff;
  text-decoration: none; 
}

.copyright {
  text-align: center;
  color: #666;
  font-size: 14px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.footer-links a {
    display: block;
    color: #333;
    text-decoration: none;
    margin-bottom: 0.5rem;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #007bff;
}

.copyright {
    font-size: 0.9em;
    color: #333;
    border-top: 1px solid #ddd;
    padding-top: 1rem;
    margin-top: 1rem;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #34495e;
}

.form-group input[type="text"] {
    width: 100%;
    padding: 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1em;
}

button[type="submit"] {
    background-color: #007bff;
    color: white;
    padding: 12px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1em;
    transition: background-color 0.3s ease;
    display: block;
    width: 100%;
}

button[type="submit"]:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

button[type="submit"]:hover:not(:disabled) {
    background-color: #0056b3;
}

/* Loading and Error Styles */
.loading-indicator, .error-message {
    text-align: center;
    margin-top: 20px;
    padding: 15px;
    border-radius: 4px;
}

.loading-indicator {
    color: #007bff;
}

.error-message {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border-left-color: #007bff;
    animation: spin 1s ease infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Styles for the results display */
.results-display {
  margin-top: 20px;
  padding: 15px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.results-display h2 {
  margin-bottom: 15px;
  color: #333;
  font-size: 1.5em;
  text-decoration: underline;
}

.results-display ul {
  list-style-type: none; 
  padding-left: 0; 
}

.results-display li {
  margin-bottom: 8px; 
  line-height: 1.6;
}

.results-display strong {
  color: #555; 
  margin-right: 5px;
  /* text-decoration: underline; */ /* Removed generic underline */
}

/* Styles for top-level list items to make them appear as cards/sections */
.results-display > ul > li {
  background-color: #f9f9f9; /* Light background for the card */
  border: 1px solid #e0e0e0; /* Border for the card */
  border-radius: 6px; /* Rounded corners */
  padding: 15px; /* Padding inside the card */
  margin-bottom: 15px; /* Space between cards */
  box-shadow: 0 2px 5px rgba(0,0,0,0.05); /* Subtle shadow */
}

/* Style for the strong tag (key name) within these top-level cards to act as a header */
.results-display > ul > li > strong {
  display: block; /* Make it a block element to take full width */
  font-size: 1.2em; /* Larger font size for the header */
  color: #333; /* Darker color for the header */
  margin-bottom: 10px; /* Space below the header */
  padding-bottom: 5px; /* Space before the border-bottom */
  border-bottom: 1px solid #eee; /* Separator line below the header */
  text-decoration: none; /* Ensure no underline from other rules */
}

/* Adjustments for nested lists within the new card structure */
.results-display > ul > li .nested-array,
.results-display > ul > li .array-display ul {
  padding-left: 15px; /* Adjust padding for nested lists if needed */
  border-left-width: 1px; /* Thinner border for nested lists */
  margin-top: 10px;
}

.results-display > ul > li .nested-array li,
.results-display > ul > li .array-display ul li {
  font-size: 0.9em; /* Slightly smaller font for nested items */
  margin-bottom: 5px;
}

/* Ensure strong tags within nested structures are not overly styled by the top-level header style */
.results-display > ul > li .nested-array strong,
.results-display > ul > li .array-display ul strong {
    display: inline; /* Keep nested strong tags inline */
    font-size: 1em; /* Reset font size if needed */
    color: #555;
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

/* Styles for nested lists (objects/arrays) - general styling */
.results-display .nested-array,
.results-display .array-display ul {
  margin-top: 5px;
  padding-left: 20px; 
  border-left: 2px solid #eee; 
}

.results-display .nested-array li,
.results-display .array-display ul li {
  font-size: 0.95em; 
}

/* Styles for automation_categorization tasks */
.automation-tasks {
  margin-top: 0.5rem;
  padding-left: 10px; 
}

.automation-tasks .task-item {
  padding: 0.75rem;
  margin-bottom: 1rem;
  background-color: #ffffff; 
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.automation-tasks .task-item p {
  margin-bottom: 0.3rem; 
  line-height: 1.5;
}

.automation-tasks .task-item p strong {
  color: #333; 
  margin-right: 5px;
  font-weight: bold; /* Ensure nested strong tags in tasks are bold but not like headers */
  font-size: 1em;
  display: inline;
  border-bottom: none;
}

/* Styles for special keys */
.special-key {
  color: #007bff; 
  font-weight: bold;
}

/* Styles for the horizontal rule */
.results-display hr {
  border: 0;
  height: 1px;
  background-color: #ccc; 
  margin: 15px 0; 
}

/* Download Button Styles */
.download-button-container {
  text-align: center; 
  margin-top: 20px; 
  margin-bottom: 20px; 
}

.download-button {
  background-color: #007bff; 
  color: white; 
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s ease;
}

.download-button:hover {
  background-color: #0056b3; 
}

.download-button:disabled {
  background-color: #cccccc;
  color: #666666;
  cursor: not-allowed;
}

/* Footer Styles from home.component.css */
footer {
  background-color: #fff;
  padding: 40px 0 20px;
  border-top: 1px solid #eee;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
}

.footer-left {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.footer-logo h3 {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin: 0;
  text-decoration: none; 
}

.footer-tagline p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.footer-right h3 {
  font-size: 18px;
  margin-bottom: 15px;
  color: #333;
  text-decoration: none; 
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.footer-links a {
  color: #666;
  text-decoration: none; 
  transition: color 0.3s;
}

.footer-links a:hover {
  color: #007bff;
  text-decoration: none; 
}

.copyright {
  text-align: center;
  color: #666;
  font-size: 14px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.footer-links a {
    display: block;
    color: #333;
    text-decoration: none;
    margin-bottom: 0.5rem;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #007bff;
}

.copyright {
    font-size: 0.9em;
    color: #333;
    border-top: 1px solid #ddd;
    padding-top: 1rem;
    margin-top: 1rem;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #34495e;
}

.form-group input[type="text"] {
    width: 100%;
    padding: 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1em;
}

button[type="submit"] {
    background-color: #007bff;
    color: white;
    padding: 12px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1em;
    transition: background-color 0.3s ease;
    display: block;
    width: 100%;
}

button[type="submit"]:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

button[type="submit"]:hover:not(:disabled) {
    background-color: #0056b3;
}

/* Loading and Error Styles */
.loading-indicator, .error-message {
    text-align: center;
    margin-top: 20px;
    padding: 15px;
    border-radius: 4px;
}

.loading-indicator {
    color: #007bff;
}

.error-message {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border-left-color: #007bff;
    animation: spin 1s ease infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Styles for the results display */
.results-display {
  margin-top: 20px;
  padding: 15px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.results-display h2 {
  margin-bottom: 15px;
  color: #333;
  font-size: 1.5em;
  text-decoration: underline;
}

.results-display ul {
  list-style-type: none; 
  padding-left: 0; 
}

.results-display li {
  margin-bottom: 8px; 
  line-height: 1.6;
}

.results-display strong {
  color: #555; 
  margin-right: 5px;
  /* text-decoration: underline; */ /* Removed generic underline */
}

/* Styles for top-level list items to make them appear as cards/sections */
.results-display > ul > li {
  background-color: #f9f9f9; /* Light background for the card */
  border: 1px solid #e0e0e0; /* Border for the card */
  border-radius: 6px; /* Rounded corners */
  padding: 15px; /* Padding inside the card */
  margin-bottom: 15px; /* Space between cards */
  box-shadow: 0 2px 5px rgba(0,0,0,0.05); /* Subtle shadow */
}

/* Style for the strong tag (key name) within these top-level cards to act as a header */
.results-display > ul > li > strong {
  display: block; /* Make it a block element to take full width */
  font-size: 1.2em; /* Larger font size for the header */
  color: #333; /* Darker color for the header */
  margin-bottom: 10px; /* Space below the header */
  padding-bottom: 5px; /* Space before the border-bottom */
  border-bottom: 1px solid #eee; /* Separator line below the header */
  text-decoration: none; /* Ensure no underline from other rules */
}

/* Adjustments for nested lists within the new card structure */
.results-display > ul > li .nested-array,
.results-display > ul > li .array-display ul {
  padding-left: 15px; /* Adjust padding for nested lists if needed */
  border-left-width: 1px; /* Thinner border for nested lists */
  margin-top: 10px;
}

.results-display > ul > li .nested-array li,
.results-display > ul > li .array-display ul li {
  font-size: 0.9em; /* Slightly smaller font for nested items */
  margin-bottom: 5px;
}

/* Ensure strong tags within nested structures are not overly styled by the top-level header style */
.results-display > ul > li .nested-array strong,
.results-display > ul > li .array-display ul strong {
    display: inline; /* Keep nested strong tags inline */
    font-size: 1em; /* Reset font size if needed */
    color: #555;
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

/* Styles for nested lists (objects/arrays) - general styling */
.results-display .nested-array,
.results-display .array-display ul {
  margin-top: 5px;
  padding-left: 20px; 
  border-left: 2px solid #eee; 
}

.results-display .nested-array li,
.results-display .array-display ul li {
  font-size: 0.95em; 
}

/* Styles for automation_categorization tasks */
.automation-tasks {
  margin-top: 0.5rem;
  padding-left: 10px; 
}

.automation-tasks .task-item {
  padding: 0.75rem;
  margin-bottom: 1rem;
  background-color: #ffffff; 
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.automation-tasks .task-item p {
  margin-bottom: 0.3rem; 
  line-height: 1.5;
}

.automation-tasks .task-item p strong {
  color: #333; 
  margin-right: 5px;
  font-weight: bold; /* Ensure nested strong tags in tasks are bold but not like headers */
  font-size: 1em;
  display: inline;
  border-bottom: none;
}

/* Styles for special keys */
.special-key {
  color: #007bff; 
  font-weight: bold;
}

/* Styles for the horizontal rule */
.results-display hr {
  border: 0;
  height: 1px;
  background-color: #ccc; 
  margin: 15px 0; 
}

/* Download Button Styles */
.download-button-container {
  text-align: center; 
  margin-top: 20px; 
  margin-bottom: 20px; 
}

.download-button {
  background-color: #007bff; 
  color: white; 
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s ease;
}

.download-button:hover {
  background-color: #0056b3; 
}

.download-button:disabled {
  background-color: #cccccc;
  color: #666666;
  cursor: not-allowed;
}

/* Footer Styles from home.component.css */
footer {
  background-color: #fff;
  padding: 40px 0 20px;
  border-top: 1px solid #eee;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
}

.footer-left {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.footer-logo h3 {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin: 0;
  text-decoration: none; 
}

.footer-tagline p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.footer-right h3 {
  font-size: 18px;
  margin-bottom: 15px;
  color: #333;
  text-decoration: none; 
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.footer-links a {
  color: #666;
  text-decoration: none; 
  transition: color 0.3s;
}

.footer-links a:hover {
  color: #007bff;
  text-decoration: none; 
}

.copyright {
  text-align: center;
  color: #666;
  font-size: 14px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.footer-links a {
    display: block;
    color: #333;
    text-decoration: none;
    margin-bottom: 0.5rem;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #007bff;
}

.copyright {
    font-size: 0.9em;
    color: #333;
    border-top: 1px solid #ddd;
    padding-top: 1rem;
    margin-top: 1rem;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #34495e;
}

.form-group input[type="text"] {
    width: 100%;
    padding: 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1em;
}

button[type="submit"] {
    background-color: #007bff;
    color: white;
    padding: 12px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1em;
    transition: background-color 0.3s ease;
    display: block;
    width: 100%;
}

button[type="submit"]:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

button[type="submit"]:hover:not(:disabled) {
    background-color: #0056b3;
}

/* Loading and Error Styles */
.loading-indicator, .error-message {
    text-align: center;
    margin-top: 20px;
    padding: 15px;
    border-radius: 4px;
}

.loading-indicator {
    color: #007bff;
}

.error-message {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border-left-color: #007bff;
    animation: spin 1s ease infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Styles for the results display */
.results-display {
  margin-top: 20px;
  padding: 15px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.results-display h2 {
  margin-bottom: 15px;
  color: #333;
  font-size: 1.5em;
  text-decoration: underline;
}

.results-display ul {
  list-style-type: none; 
  padding-left: 0; 
}

.results-display li {
  margin-bottom: 8px; 
  line-height: 1.6;
}

.results-display strong {
  color: #555; 
  margin-right: 5px;
  /* text-decoration: underline; */ /* Removed generic underline */
}

/* Styles for top-level list items to make them appear as cards/sections */
.results-display > ul > li {
  background-color: #f9f9f9; /* Light background for the card */
  border: 1px solid #e0e0e0; /* Border for the card */
  border-radius: 6px; /* Rounded corners */
  padding: 15px; /* Padding inside the card */
  margin-bottom: 15px; /* Space between cards */
  box-shadow: 0 2px 5px rgba(0,0,0,0.05); /* Subtle shadow */
}

/* Style for the strong tag (key name) within these top-level cards to act as a header */
.results-display > ul > li > strong {
  display: block; /* Make it a block element to take full width */
  font-size: 1.2em; /* Larger font size for the header */
  color: #333; /* Darker color for the header */
  margin-bottom: 10px; /* Space below the header */
  padding-bottom: 5px; /* Space before the border-bottom */
  border-bottom: 1px solid #eee; /* Separator line below the header */
  text-decoration: none; /* Ensure no underline from other rules */
}

/* Adjustments for nested lists within the new card structure */
.results-display > ul > li .nested-array,
.results-display > ul > li .array-display ul {
  padding-left: 15px; /* Adjust padding for nested lists if needed */
  border-left-width: 1px; /* Thinner border for nested lists */
  margin-top: 10px;
}

.results-display > ul > li .nested-array li,
.results-display > ul > li .array-display ul li {
  font-size: 0.9em; /* Slightly smaller font for nested items */
  margin-bottom: 5px;
}

/* Ensure strong tags within nested structures are not overly styled by the top-level header style */
.results-display > ul > li .nested-array strong,
.results-display > ul > li .array-display ul strong {
    display: inline; /* Keep nested strong tags inline */
    font-size: 1em; /* Reset font size if needed */
    color: #555;
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

/* Styles for nested lists (objects/arrays) - general styling */
.results-display .nested-array,
.results-display .array-display ul {
  margin-top: 5px;
  padding-left: 20px; 
  border-left: 2px solid #eee; 
}

.results-display .nested-array li,
.results-display .array-display ul li {
  font-size: 0.95em; 
}

/* Styles for automation_categorization tasks */
.automation-tasks {
  margin-top: 0.5rem;
  padding-left: 10px; 
}

.automation-tasks .task-item {
  padding: 0.75rem;
  margin-bottom: 1rem;
  background-color: #ffffff; 
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.automation-tasks .task-item p {
  margin-bottom: 0.3rem; 
  line-height: 1.5;
}

.automation-tasks .task-item p strong {
  color: #333; 
  margin-right: 5px;
  font-weight: bold; /* Ensure nested strong tags in tasks are bold but not like headers */
  font-size: 1em;
  display: inline;
  border-bottom: none;
}

/* Styles for special keys */
.special-key {
  color: #007bff; 
  font-weight: bold;
}

/* Styles for the horizontal rule */
.results-display hr {
  border: 0;
  height: 1px;
  background-color: #ccc; 
  margin: 15px 0; 
}

/* Download Button Styles */
.download-button-container {
  text-align: center; 
  margin-top: 20px; 
  margin-bottom: 20px; 
}

.download-button {
  background-color: #007bff; 
  color: white; 
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s ease;
}

.download-button:hover {
  background-color: #0056b3; 
}

.download-button:disabled {
  background-color: #cccccc;
  color: #666666;
  cursor: not-allowed;
}

/* Footer Styles from home.component.css */
footer {
  background-color: #fff;
  padding: 40px 0 20px;
  border-top: 1px solid #eee;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
}

.footer-left {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.footer-logo h3 {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin: 0;
  text-decoration: none; 
}

.footer-tagline p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.footer-right h3 {
  font-size: 18px;
  margin-bottom: 15px;
  color: #333;
  text-decoration: none; 
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.footer-links a {
  color: #666;
  text-decoration: none; 
  transition: color 0.3s;
}

.footer-links a:hover {
  color: #007bff;
  text-decoration: none; 
}

.copyright {
  text-align: center;
  color: #666;
  font-size: 14px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.footer-links a {
    display: block;
    color: #333;
    text-decoration: none;
    margin-bottom: 0.5rem;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #007bff;
}

.copyright {
    font-size: 0.9em;
    color: #333;
    border-top: 1px solid #ddd;
    padding-top: 1rem;
    margin-top: 1rem;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #34495e;
}

.form-group input[type="text"] {
    width: 100%;
    padding: 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1em;
}

button[type="submit"] {
    background-color: #007bff;
    color: white;
    padding: 12px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1em;
    transition: background-color 0.3s ease;
    display: block;
    width: 100%;
}

button[type="submit"]:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

button[type="submit"]:hover:not(:disabled) {
    background-color: #0056b3;
}

/* Loading and Error Styles */
.loading-indicator, .error-message {
    text-align: center;
    margin-top: 20px;
    padding: 15px;
    border-radius: 4px;
}

.loading-indicator {
    color: #007bff;
}

.error-message {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border-left-color: #007bff;
    animation: spin 1s ease infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Styles for the results display */
.results-display {
  margin-top: 20px;
  padding: 15px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.results-display h2 {
  margin-bottom: 15px;
  color: #333;
  font-size: 1.5em;
  text-decoration: underline;
}

.results-display ul {
  list-style-type: none; 
  padding-left: 0; 
}

.results-display li {
  margin-bottom: 8px; 
  line-height: 1.6;
}

.results-display strong {
  color: #555; 
  margin-right: 5px;
  /* text-decoration: underline; */ /* Removed generic underline */
}

/* Styles for top-level list items to make them appear as cards/sections */
.results-display > ul > li {
  background-color: #f9f9f9; /* Light background for the card */
  border: 1px solid #e0e0e0; /* Border for the card */
  border-radius: 6px; /* Rounded corners */
  padding: 15px; /* Padding inside the card */
  margin-bottom: 15px; /* Space between cards */
  box-shadow: 0 2px 5px rgba(0,0,0,0.05); /* Subtle shadow */
}

/* Enhanced styles for automation analysis section - matching other sections */
.automation-analysis-section {
  margin-top: 10px;
  background-color: inherit; /* Match parent background */
  border: none; /* Remove border to match other sections */
  border-radius: 0; /* Remove border radius to match other sections */
  overflow: visible; /* Remove overflow hidden */
}

.automation-tasks-formatted {
  background-color: inherit; /* Match parent background */
  color: #333;
  font-family: Arial, sans-serif; /* Match other sections font */
  font-size: inherit; /* Match other sections font size */
  line-height: 1.6;
  margin: 0;
  padding: 0; /* Remove padding to match other sections */
  white-space: pre-wrap;
  word-wrap: break-word;
  border: none;
  border-radius: 0;
  max-height: none; /* Remove max-height to prevent scrolling */
  overflow: visible; /* Remove scrolling */
}